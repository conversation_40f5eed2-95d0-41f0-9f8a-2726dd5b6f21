import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCheck, FaTimes, FaFilter, FaEye, FaMoneyBill } from 'react-icons/fa';

const ChequeStatement = () => {
  const [chequeStatements, setChequeStatements] = useState([]);
  const [filteredStatements, setFilteredStatements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [filters, setFilters] = useState({
    status: '',
    fromDate: '',
    toDate: '',
    referType: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchChequeStatements();
    fetchStatistics();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [chequeStatements, filters]);

  const fetchChequeStatements = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:8000/api/cheque-statements');
      if (response.data.success) {
        setChequeStatements(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching cheque statements:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await axios.get('http://localhost:8000/api/cheque-statements/statistics');
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const applyFilters = () => {
    let filtered = [...chequeStatements];

    if (filters.status) {
      filtered = filtered.filter(statement => statement.status === filters.status);
    }

    if (filters.fromDate) {
      filtered = filtered.filter(statement =>
        new Date(statement.payment_date) >= new Date(filters.fromDate)
      );
    }

    if (filters.toDate) {
      filtered = filtered.filter(statement =>
        new Date(statement.payment_date) <= new Date(filters.toDate)
      );
    }

    if (filters.referType) {
      filtered = filtered.filter(statement => statement.refer_type === filters.referType);
    }

    setFilteredStatements(filtered);
  };

  const handleStatusUpdate = async (id, newStatus) => {
    try {
      setLoading(true);
      const endpoint = newStatus === 'completed'
        ? `http://localhost:8000/api/cheque-statements/${id}/complete`
        : `http://localhost:8000/api/cheque-statements/${id}/decline`;

      const response = await axios.patch(endpoint);

      if (response.data.success) {
        // Update the local state
        setChequeStatements(prev =>
          prev.map(statement =>
            statement.id === id
              ? { ...statement, status: newStatus }
              : statement
          )
        );

        // Refresh statistics
        fetchStatistics();
      }
    } catch (error) {
      console.error('Error updating cheque status:', error);
      alert('Failed to update cheque status');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-LK', {
      style: 'currency',
      currency: 'LKR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-GB');
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: 'Pending' },
      completed: { color: 'bg-green-100 text-green-800', text: 'Completed' },
      declined: { color: 'bg-red-100 text-red-800', text: 'Declined' }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  return (
    <div className="min-h-screen p-4 bg-gray-50">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="p-6 mb-6 bg-white rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Cheque Statement</h1>
              <p className="mt-1 text-gray-600">Manage and track cheque payments</p>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
            >
              <FaFilter />
              Filters
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-4">
          <div className="p-4 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Cheques</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.total_cheques || 0}</p>
              </div>
              <FaMoneyBill className="text-2xl text-blue-500" />
            </div>
          </div>

          <div className="p-4 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{statistics.pending_cheques || 0}</p>
                <p className="text-xs text-gray-500">{formatCurrency(statistics.pending_amount || 0)}</p>
              </div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            </div>
          </div>

          <div className="p-4 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">{statistics.completed_cheques || 0}</p>
                <p className="text-xs text-gray-500">{formatCurrency(statistics.completed_amount || 0)}</p>
              </div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>

          <div className="p-4 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Declined</p>
                <p className="text-2xl font-bold text-red-600">{statistics.declined_cheques || 0}</p>
                <p className="text-xs text-gray-500">{formatCurrency(statistics.declined_amount || 0)}</p>
              </div>
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="p-6 mb-6 bg-white rounded-lg shadow-sm"
            >
              <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-700">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="declined">Declined</option>
                  </select>
                </div>

                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-700">From Date</label>
                  <input
                    type="date"
                    value={filters.fromDate}
                    onChange={(e) => setFilters(prev => ({ ...prev, fromDate: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-700">To Date</label>
                  <input
                    type="date"
                    value={filters.toDate}
                    onChange={(e) => setFilters(prev => ({ ...prev, toDate: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-700">Type</label>
                  <select
                    value={filters.referType}
                    onChange={(e) => setFilters(prev => ({ ...prev, referType: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Types</option>
                    <option value="Customer">Customer</option>
                    <option value="Supplier">Supplier</option>
                    <option value="Ledger">Ledger</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end mt-4">
                <button
                  onClick={() => setFilters({ status: '', fromDate: '', toDate: '', referType: '' })}
                  className="px-4 py-2 text-gray-600 transition-colors bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Clear Filters
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Cheque Statements Table */}
        <div className="overflow-hidden bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Cheque Statements ({filteredStatements.length})
            </h2>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-b-2 border-blue-600 rounded-full animate-spin"></div>
            </div>
          ) : filteredStatements.length === 0 ? (
            <div className="py-12 text-center">
              <FaEye className="mx-auto mb-4 text-4xl text-gray-400" />
              <p className="text-gray-500">No cheque statements found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Voucher Details
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Reference
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Cheque Details
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Status
                    </th>
                    <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredStatements.map((statement) => (
                    <tr key={statement.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {statement.voucher_no}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatDate(statement.payment_date)}
                          </div>
                          <div className="text-xs text-gray-400">
                            {statement.refer_type}: {statement.refer_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {statement.reference_no}
                          </div>
                          <div className="text-sm text-gray-500">
                            {statement.transaction_type}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {statement.cheque_no}
                          </div>
                          <div className="text-sm text-gray-500">
                            {statement.bank_name}
                          </div>
                          <div className="text-xs text-gray-400">
                            Issue: {formatDate(statement.issue_date)}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(statement.amount)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(statement.status)}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                        {statement.status === 'pending' && (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleStatusUpdate(statement.id, 'completed')}
                              disabled={loading}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                            >
                              <FaCheck className="mr-1" />
                              Pass
                            </button>
                            <button
                              onClick={() => handleStatusUpdate(statement.id, 'declined')}
                              disabled={loading}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                            >
                              <FaTimes className="mr-1" />
                              Decline
                            </button>
                          </div>
                        )}
                        {statement.status !== 'pending' && (
                          <span className="text-xs text-gray-400">
                            {statement.status === 'completed' ? 'Passed' : 'Declined'}
                          </span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChequeStatement;
