import React, { useState, useEffect } from 'react';
import axios from 'axios';
import PrintStatement from './PrintStatement';

const Statement = () => {
  // State for form inputs
  const [personType, setPersonType] = useState('');
  const [selectedPerson, setSelectedPerson] = useState('');
  const [selectedPersonId, setSelectedPersonId] = useState('');
  const [fromDate, setFromDate] = useState(`${new Date().getFullYear()}-01-01`);
  const [toDate, setToDate] = useState(new Date().toISOString().split('T')[0]);
  const [showTable, setShowTable] = useState(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [filteredPeople, setFilteredPeople] = useState([]);
  const [people, setPeople] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [openingBalanceDetails, setOpeningBalanceDetails] = useState({
    initial_opening_balance: 0,
    past_year_balance: 0,
  });

  // Calculate totals
  const totalEntries = transactions.length;
  const totalDebit = transactions.reduce((sum, t) => sum + (parseFloat(t.debit) || 0), 0);
  const totalCredit = transactions.reduce((sum, t) => sum + (parseFloat(t.credit) || 0), 0);
  const totalBalance = transactions.length > 0 ? transactions[transactions.length - 1].balance : 0;

  // Fetch people based on person type
  const fetchPeople = async (type) => {
    try {
      setLoading(true);
      let endpoint = '';
      switch (type) {
        case 'Customer':
          endpoint = 'http://localhost:8000/api/customers';
          break;
        case 'Supplier':
          endpoint = 'http://localhost:8000/api/suppliers';
          break;
        case 'Other':
          endpoint = 'http://localhost:8000/api/staff-ledger';
          break;
        default:
          return;
      }

      const response = await axios.get(endpoint);
      const data = response.data.data || response.data;
      setPeople(data);
      setFilteredPeople(data);
    } catch (error) {
      console.error('Error fetching people:', error);
      setError('Failed to fetch people data');
    } finally {
      setLoading(false);
    }
  };

  // Generate statement
  const generateStatement = async () => {
    if (!selectedPersonId || !personType) {
      setError('Please select a person');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const response = await axios.post('http://localhost:8000/api/statement/generate', {
        person_type: personType,
        person_id: selectedPersonId,
        from_date: fromDate,
        to_date: toDate,
      });

      setTransactions(response.data.transactions || []);
      setOpeningBalanceDetails(response.data.opening_balance_details || {
        initial_opening_balance: 0,
        past_year_balance: 0,
      });
      setShowTable(true);
    } catch (error) {
      console.error('Error generating statement:', error);
      setError(error.response?.data?.message || 'Failed to generate statement');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    generateStatement();
  };

  const handleReset = () => {
    setPersonType('');
    setSelectedPerson('');
    setSelectedPersonId('');
    setFromDate(`${new Date().getFullYear()}-01-01`);
    setToDate(new Date().toISOString().split('T')[0]);
    setShowTable(false);
    setIsDropdownVisible(false);
    setPeople([]);
    set26
    setFilteredPeople([]);
    setTransactions([]);
    setError('');
    setOpeningBalanceDetails({ initial_opening_balance: 0, past_year_balance: 0 });
    setShowModal(false);
  };

  const handlePersonTypeChange = (e) => {
    const type = e.target.value;
    setPersonType(type);
    setSelectedPerson('');
    setSelectedPersonId('');
    setFilteredPeople([]);
    setShowTable(false);
    setTransactions([]);
    setOpeningBalanceDetails({ initial_opening_balance: 0, past_year_balance: 0 });
    setShowModal(false);

    if (type) {
      fetchPeople(type);
    }
  };

  const handlePersonInputChange = (e) => {
    const value = e.target.value;
    setSelectedPerson(value);

    if (personType && people.length > 0) {
      const filtered = people.filter((person) => {
        const name = getPersonName(person);
        return name.toLowerCase().includes(value.toLowerCase());
      });
      setFilteredPeople(filtered);
    }

    setIsDropdownVisible(true);
  };

  const getPersonName = (person) => {
    if (personType === 'Customer') return person.customer_name;
    if (personType === 'Supplier') return person.supplier_name;
    if (personType === 'Other') return person.name;
    return '';
  };

  const getPersonId = (person) => {
    if (personType === 'Customer') return person.id;
    if (personType === 'Supplier') return person.id;
    if (personType === 'Other') return person.staff_id;
    return '';
  };

  const selectPerson = (person) => {
    const name = getPersonName(person);
    const id = getPersonId(person);
    setSelectedPerson(name);
    setSelectedPersonId(id);
    setIsDropdownVisible(false);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleOpeningBalanceClick = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container px-3 py-4 mx-auto sm:px-4 md:px-6 lg:px-8 max-w-7xl">
        <h1 className="mb-6 text-2xl font-bold text-center text-gray-800 sm:mb-8 sm:text-3xl md:text-3xl lg:text-3xl xl:mb-10">
          Statement Generator
        </h1>

        {error && (
          <div className="p-4 mb-4 text-red-700 bg-red-100 border border-red-400 rounded-lg">
            {error}
          </div>
        )}

        <form className="grid grid-cols-1 gap-4 p-4 mb-6 bg-white rounded-lg shadow-lg sm:gap-6 sm:p-6 sm:mb-8 md:grid-cols-2 lg:gap-8 lg:p-8 xl:grid-cols-4 2xl:gap-10 sm:w-[100%]">
          <div className="relative">
            <label className="block mb-2 text-sm font-medium text-gray-700 sm:mb-3 md:text-base">Person Type</label>
            <select
              value={personType}
              onChange={handlePersonTypeChange}
              disabled={loading}
              className="block w-full px-4 py-3 text-sm transition-all duration-200 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 disabled:bg-gray-100 disabled:cursor-not-allowed sm:text-base md:py-4 lg:text-md"
            >
              <option value="">Select Type</option>
              <option value="Customer">Customer</option>
              <option value="Supplier">Supplier</option>
              <option value="Other">Other</option>
            </select>
          </div>

          <div className="relative">
            <label className="block mb-2 text-sm font-medium text-gray-700 sm:mb-3 md:text-base">Person</label>
            <div className="relative">
              <input
                type="text"
                value={selectedPerson}
                onChange={handlePersonInputChange}
                onFocus={() => setIsDropdownVisible(true)}
                onBlur={() => setTimeout(() => setIsDropdownVisible(false), 200)}
                disabled={!personType || loading}
                className="block w-full px-4 py-3 text-sm transition-all duration-200 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:hover:border-gray-300 sm:text-base md:py-4 lg:text-lg"
                placeholder={loading ? 'Loading...' : 'Type or select a person'}
              />
              {isDropdownVisible && filteredPeople.length > 0 && (
                <ul className="absolute z-20 w-full py-1 mt-1 overflow-auto text-sm bg-white border border-gray-200 rounded-lg shadow-xl max-h-48 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-base sm:max-h-60 md:max-h-72">
                  {filteredPeople.map((person) => (
                    <li
                      key={getPersonId(person)}
                      className="relative py-3 pl-4 pr-4 text-gray-900 transition-colors duration-150 cursor-pointer select-none hover:bg-indigo-50 hover:text-indigo-900 md:py-4"
                      onMouseDown={() => selectPerson(person)}
                    >
                      {getPersonName(person)}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700 sm:mb-3 md:text-base">From Date</label>
            <input
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              className="block w-full px-4 py-3 text-sm transition-all duration-200 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 sm:text-base md:py-4 lg:text-lg"
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700 sm:mb-3 md:text-base">To Date</label>
            <input
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              className="block w-full px-4 py-3 text-sm transition-all duration-200 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 sm:text-base md:py-4 lg:text-lg"
            />
          </div>

          <div className="flex flex-col items-stretch col-span-1 space-y-4 md:col-span-2 xl:col-span-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4 lg:space-x-6">
            <button
              type="button"
              onClick={handleReset}
              className="w-full px-6 py-3 text-sm font-medium text-white transition-all duration-200 bg-gray-600 rounded-lg shadow-md hover:bg-gray-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:w-auto md:px-8 md:py-4 md:text-base lg:text-md"
            >
              Reset
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={!personType || !selectedPerson || loading}
              className="w-full px-6 py-3 text-sm font-medium text-white transition-all duration-200 bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-md sm:w-auto md:px-8 md:py-4 md:text-base lg:text-md"
            >
              {loading ? 'Generating...' : 'Generate Statement'}
            </button>
            {showTable && (
              <button
                type="button"
                onClick={handlePrint}
                className="w-full px-6 py-3 text-sm font-medium text-white transition-all duration-200 bg-green-600 rounded-lg shadow-md hover:bg-green-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:w-auto md:px-8 md:py-4 md:text-base lg:text-md"
              >
                Print Statement
              </button>
            )}
          </div>
        </form>

        {showTable && (
          <>
            {/* Desktop Table View */}
            <div className="hidden overflow-hidden bg-white border border-gray-200 shadow-lg rounded-xl md:block lg:shadow-xl">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr>
                      <th scope="col" className="px-4 py-4 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-5 lg:text-sm xl:px-8">Date</th>
                      <th scope="col" className="px-4 py-4 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-5 lg:text-sm xl:px-8">Type</th>
                      <th scope="col" className="px-4 py-4 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-5 lg:text-sm xl:px-8">Description</th>
                      <th scope="col" className="px-4 py-4 text-xs font-semibold tracking-wider text-right text-gray-600 uppercase md:px-6 lg:py-5 lg:text-sm xl:px-8">Debit</th>
                      <th scope="col" className="px-4 py-4 text-xs font-semibold tracking-wider text-right text-gray-600 uppercase md:px-6 lg:py-5 lg:text-sm xl:px-8">Credit</th>
                      <th scope="col" className="px-4 py-4 text-xs font-semibold tracking-wider text-right text-gray-600 uppercase md:px-6 lg:py-5 lg:text-sm xl:px-8">Balance</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transactions.map((transaction, index) => (
                      <tr
                        key={index}
                        className={`transition-colors duration-150 hover:bg-gray-50 ${transaction.is_opening_balance ? 'cursor-pointer' : ''}`}
                        onClick={transaction.is_opening_balance ? handleOpeningBalanceClick : undefined}
                      >
                        <td className="px-4 py-4 text-sm text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">{transaction.date}</td>
                        <td className="px-4 py-4 text-sm text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                          {transaction.type && (
                            <span className="inline-flex px-2 py-1 text-xs font-medium text-indigo-800 bg-indigo-100 rounded-full lg:text-sm">
                              {transaction.type}
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-700 md:px-6 lg:py-5 lg:text-base xl:px-8">
                          {transaction.description}
                          {transaction.is_opening_balance && (
                            <span className="ml-2 text-xs text-indigo-600 underline"> (Click for details)</span>
                          )}
                        </td>
                        <td className="px-4 py-4 text-sm font-medium text-right text-green-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                          {transaction.debit ? `Rs ${parseFloat(transaction.debit).toLocaleString()}` : '-'}
                        </td>
                        <td className="px-4 py-4 text-sm font-medium text-right text-red-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                          {transaction.credit ? `Rs ${parseFloat(transaction.credit).toLocaleString()}` : '-'}
                        </td>
                        <td className="px-4 py-4 text-sm font-semibold text-right text-blue-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                          Rs {parseFloat(transaction.balance).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr className="border-t-2 border-gray-300">
                      <td className="px-4 py-4 text-sm font-semibold text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                        Total Entries: <span className="text-indigo-600">{totalEntries}</span>
                      </td>
                      <td className="px-4 py-4 md:px-6 lg:py-5 xl:px-8"></td>
                      <td className="px-4 py-4 md:px-6 lg:py-5 xl:px-8"></td>
                      <td className="px-4 py-4 text-sm font-semibold text-right text-green-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                        Rs {totalDebit.toLocaleString()}
                      </td>
                      <td className="px-4 py-4 text-sm font-semibold text-right text-red-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                        Rs {totalCredit.toLocaleString()}
                      </td>
                      <td className="px-4 py-4 text-sm font-bold text-right text-blue-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base xl:px-8">
                        Rs {totalBalance.toLocaleString()}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Mobile Card View */}
            <div className="space-y-4 md:hidden">
              {transactions.map((transaction, index) => (
                <div
                  key={index}
                  className={`p-5 transition-shadow duration-200 bg-white border border-gray-200 shadow-md rounded-xl hover:shadow-lg ${transaction.is_opening_balance ? 'cursor-pointer' : ''}`}
                  onClick={transaction.is_opening_balance ? handleOpeningBalanceClick : undefined}
                >
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-base font-semibold text-gray-900 sm:text-lg">{transaction.date}</span>
                    {transaction.type && (
                      <span className="px-3 py-1 text-xs font-medium text-indigo-800 bg-indigo-100 rounded-full sm:text-sm">
                        {transaction.type}
                      </span>
                    )}
                  </div>
                  <div className="mb-4">
                    <p className="text-sm leading-relaxed text-gray-600 sm:text-base">
                      {transaction.description}
                      {transaction.is_opening_balance && (
                        <span className="ml-2 text-xs text-indigo-600 underline"> (Tap for details)</span>
                      )}
                    </p>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm sm:gap-6">
                    <div className="p-3 text-center rounded-lg bg-green-50">
                      <p className="mb-1 text-xs font-semibold tracking-wide text-gray-600 uppercase">Debit</p>
                      <p className="text-base font-bold text-green-600 sm:text-lg">
                        {transaction.debit ? `Rs ${parseFloat(transaction.debit).toLocaleString()}` : '-'}
                      </p>
                    </div>
                    <div className="p-3 text-center rounded-lg bg-red-50">
                      <p className="mb-1 text-xs font-semibold tracking-wide text-gray-600 uppercase">Credit</p>
                      <p className="text-base font-bold text-red-600 sm:text-lg">
                        {transaction.credit ? `Rs ${parseFloat(transaction.credit).toLocaleString()}` : '-'}
                      </p>
                    </div>
                    <div className="p-3 text-center rounded-lg bg-blue-50">
                      <p className="mb-1 text-xs font-semibold tracking-wide text-gray-600 uppercase">Balance</p>
                      <p className="text-base font-bold text-blue-600 sm:text-lg">
                        Rs {parseFloat(transaction.balance).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}

              {/* Mobile Summary Card */}
              <div className="p-6 border-2 border-gray-300 shadow-lg rounded-xl bg-gradient-to-br from-gray-50 to-gray-100">
                <h3 className="mb-4 text-lg font-bold text-center text-gray-900 sm:text-xl">Transaction Summary</h3>
                <div className="grid grid-cols-2 gap-4 text-sm sm:gap-6">
                  <div className="p-4 text-center bg-white rounded-lg shadow-sm">
                    <p className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">Total Entries</p>
                    <p className="text-xl font-bold text-indigo-600 sm:text-2xl">{totalEntries}</p>
                  </div>
                  <div className="p-4 text-center bg-white rounded-lg shadow-sm">
                    <p className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">Final Balance</p>
                    <p className="text-xl font-bold text-blue-600 sm:text-2xl">Rs {totalBalance.toLocaleString()}</p>
                  </div>
                  <div className="p-4 text-center bg-white rounded-lg shadow-sm">
                    <p className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">Total Debit</p>
                    <p className="text-xl font-bold text-green-600 sm:text-2xl">Rs {totalDebit.toLocaleString()}</p>
                  </div>
                  <div className="p-4 text-center bg-white rounded-lg shadow-sm">
                    <p className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase">Total Credit</p>
                    <p className="text-xl font-bold text-red-600 sm:text-2xl">Rs {totalCredit.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal for Opening Balance Details */}
            {showModal && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
                  <h2 className="mb-4 text-lg font-bold text-gray-900">Opening Balance Details</h2>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-700">Initial Opening Balance:</span>
                      <span className="text-sm font-semibold text-blue-600">
                        Rs {parseFloat(openingBalanceDetails.initial_opening_balance).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-700">Past Year Balance ({fromDate.split('-')[0] - 1}):</span>
                      <span className="text-sm font-semibold text-blue-600">
                        Rs {parseFloat(openingBalanceDetails.past_year_balance).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between pt-2 border-t">
                      <span className="text-sm font-bold text-gray-700">Total Opening Balance:</span>
                      <span className="text-sm font-bold text-blue-600">
                        Rs {(parseFloat(openingBalanceDetails.initial_opening_balance) + parseFloat(openingBalanceDetails.past_year_balance)).toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <div className="mt-6 text-right">
                    <button
                      onClick={closeModal}
                      className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {showTable && (
          <div className="hidden print:block">
            <PrintStatement
              personType={personType}
              selectedPerson={selectedPerson}
              fromDate={fromDate}
              toDate={toDate}
              transactions={transactions}
              totalEntries={totalEntries}
              totalDebit={totalDebit}
              totalCredit={totalCredit}
              totalBalance={totalBalance}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Statement;