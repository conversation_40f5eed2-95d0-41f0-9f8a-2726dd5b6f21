import React, { useState, useEffect } from "react";
import axios from "axios";

const GenerateLoyaltyCard = () => {
  // State to manage form inputs
  const [cardDetails, setCardDetails] = useState({
    id: null,
    cardName: "",
    calculationType: "Point-wise",
    pointCalculationMode: "Threshold-wise",
    thresholdMethod: "per-threshold",
    pointsPerThreshold: "",
    pointsPerThresholdValue: "",
    singleThresholdAmount: "",
    singleThresholdPoints: "",
    singleThresholdPercentage: "",
    singleThresholdOperator: ">",
    percentagePerThreshold: "",
    percentagePerThresholdValue: "",
    ranges: [
      { minRange: "", maxRange: "", points: "", discountPercentage: "" },
    ],
  });

  const [configurations, setConfigurations] = useState([]);
  const [viewCard, setViewCard] = useState(null);
  const [activeTab, setActiveTab] = useState("create");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch configurations on component mount
  useEffect(() => {
    fetchConfigurations();
  }, []);

  const fetchConfigurations = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get("http://localhost:8000/api/loyalty-cards");
      const mappedConfigs = response.data.map((config) => ({
        ...config,
        ranges: config.ranges
          ? config.ranges.map((range) => ({
              id: range.id,
              minRange: range.min_range,
              maxRange: range.max_range,
              points: range.points,
              discountPercentage: range.discount_percentage,
            }))
          : [],
      }));
      setConfigurations(mappedConfigs);
    } catch (error) {
      console.error("Error fetching configurations:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCardDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRangeChange = (index, e) => {
    const { name, value } = e.target;
    const updatedRanges = [...cardDetails.ranges];
    updatedRanges[index] = { ...updatedRanges[index], [name]: value };
    setCardDetails((prev) => ({
      ...prev,
      ranges: updatedRanges,
    }));
  };

  const addRange = () => {
    setCardDetails((prev) => ({
      ...prev,
      ranges: [
        ...prev.ranges,
        { minRange: "", maxRange: "", points: "", discountPercentage: "" },
      ],
    }));
  };

  const calculatePoints = (billAmount) => {
    const threshold = parseInt(cardDetails.pointsPerThreshold) || 1;
    const pointsPerThreshold = parseInt(cardDetails.pointsPerThresholdValue) || 1;
    return Math.floor(parseInt(billAmount) / threshold) * pointsPerThreshold;
  };

  const handleSave = async () => {
    if (!cardDetails.cardName) {
      alert("Please enter a card name.");
      return;
    }

    if (cardDetails.calculationType === "Point-wise") {
      if (cardDetails.thresholdMethod === "per-threshold") {
        if (!cardDetails.pointsPerThreshold || !cardDetails.pointsPerThresholdValue) {
          alert("Please enter both the threshold and points per threshold for Points per Threshold configuration.");
        return;
      }
        if (parseInt(cardDetails.pointsPerThreshold) <= 0 || parseInt(cardDetails.pointsPerThresholdValue) <= 0) {
          alert("Threshold and points per threshold must be greater than 0.");
            return;
          }
      } else if (cardDetails.thresholdMethod === "single-threshold") {
        if (!cardDetails.singleThresholdAmount || !cardDetails.singleThresholdPoints) {
          alert("Please enter both the threshold amount and points for Single Threshold Logic.");
            return;
          }
        if (parseInt(cardDetails.singleThresholdAmount) <= 0 || parseInt(cardDetails.singleThresholdPoints) <= 0) {
          alert("Threshold amount and points must be greater than 0.");
            return;
          }
        }
      }
    
    if (cardDetails.calculationType === "Percentage-wise") {
      if (cardDetails.thresholdMethod === "per-threshold") {
        if (!cardDetails.percentagePerThreshold || !cardDetails.percentagePerThresholdValue) {
          alert("Please enter both the threshold and percentage per threshold for Percentage per Threshold configuration.");
        return;
      }
        if (parseInt(cardDetails.percentagePerThreshold) <= 0 || parseFloat(cardDetails.percentagePerThresholdValue) < 0 || parseFloat(cardDetails.percentagePerThresholdValue) > 100) {
          alert("Threshold must be greater than 0 and percentage must be between 0 and 100.");
        return;
      }
      } else if (cardDetails.thresholdMethod === "single-threshold") {
        if (!cardDetails.singleThresholdAmount || !cardDetails.singleThresholdPercentage) {
          alert("Please enter both the threshold amount and percentage for Single Threshold Logic.");
        return;
      }
        if (parseInt(cardDetails.singleThresholdAmount) <= 0 || parseFloat(cardDetails.singleThresholdPercentage) < 0 || parseFloat(cardDetails.singleThresholdPercentage) > 100) {
          alert("Threshold amount must be greater than 0 and percentage must be between 0 and 100.");
            return;
        }
      }
    }

    try {
      setIsLoading(true);
      const payload = {
        card_name: cardDetails.cardName,
        calculation_type: cardDetails.calculationType,
        point_calculation_mode: 'Threshold-wise',
        threshold_method: cardDetails.thresholdMethod,
        points_per_threshold: cardDetails.pointsPerThreshold
          ? parseInt(cardDetails.pointsPerThreshold)
          : null,
        points_per_threshold_value: cardDetails.pointsPerThresholdValue
          ? parseInt(cardDetails.pointsPerThresholdValue)
          : null,
        single_threshold_amount: cardDetails.singleThresholdAmount
          ? parseFloat(cardDetails.singleThresholdAmount)
          : null,
        single_threshold_points: cardDetails.singleThresholdPoints
          ? parseInt(cardDetails.singleThresholdPoints)
          : null,
        single_threshold_percentage: cardDetails.singleThresholdPercentage
          ? parseFloat(cardDetails.singleThresholdPercentage)
          : null,
        single_threshold_operator: cardDetails.singleThresholdOperator || '>',
        percentage_per_threshold: cardDetails.percentagePerThreshold
          ? parseInt(cardDetails.percentagePerThreshold)
          : null,
        percentage_per_threshold_value: cardDetails.percentagePerThresholdValue
          ? parseFloat(cardDetails.percentagePerThresholdValue)
          : null,
      };

      // Only keep relevant fields for the selected calculation mode
      if (cardDetails.thresholdMethod === 'per-threshold') {
        payload.single_threshold_amount = null;
        payload.single_threshold_points = null;
        payload.single_threshold_percentage = null;
        payload.single_threshold_operator = null;
      } else if (cardDetails.thresholdMethod === 'single-threshold') {
        payload.points_per_threshold = null;
        payload.points_per_threshold_value = null;
        payload.percentage_per_threshold = null;
        payload.percentage_per_threshold_value = null;
      }

      if (cardDetails.id) {
        await axios.put(
          `http://localhost:8000/api/loyalty-cards/${cardDetails.id}`,
          payload
        );
        alert("Loyalty card updated successfully!");
      } else {
        await axios.post("http://localhost:8000/api/loyalty-cards", payload);
        alert("Loyalty card configuration saved successfully!");
      }
      fetchConfigurations();
      setCardDetails({
        id: null,
        cardName: "",
        calculationType: "Point-wise",
        pointCalculationMode: "Threshold-wise",
        thresholdMethod: "per-threshold",
        pointsPerThreshold: "",
        pointsPerThresholdValue: "",
        singleThresholdAmount: "",
        singleThresholdPoints: "",
        singleThresholdPercentage: "",
        singleThresholdOperator: ">",
        percentagePerThreshold: "",
        percentagePerThresholdValue: "",
        ranges: [
          { minRange: "", maxRange: "", points: "", discountPercentage: "" },
        ],
      });
      setActiveTab("view");
    } catch (error) {
      console.error("Error saving configuration:", error);
      alert("Failed to save loyalty card configuration.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (config) => {
    setCardDetails({
      id: config.id,
      cardName: config.card_name,
      calculationType: config.calculation_type,
      pointCalculationMode: config.point_calculation_mode,
      thresholdMethod: config.threshold_method || "per-threshold",
      pointsPerThreshold: config.points_per_threshold || "",
      pointsPerThresholdValue: config.points_per_threshold_value || "",
      singleThresholdAmount: config.single_threshold_amount || "",
      singleThresholdPoints: config.single_threshold_points || "",
      singleThresholdPercentage: config.single_threshold_percentage || "",
      singleThresholdOperator: config.single_threshold_operator || ">",
      percentagePerThreshold: config.percentage_per_threshold || "",
      percentagePerThresholdValue: config.percentage_per_threshold_value || "",
      ranges: config.ranges.map((range) => ({
        id: range.id,
        minRange: range.min_range,
        maxRange: range.max_range,
        points: range.points,
        discountPercentage: range.discount_percentage,
      })),
    });
    setActiveTab("create");
  };

  const handleDelete = async (id) => {
    if (window.confirm("Are you sure you want to delete this loyalty card?")) {
      try {
        setIsLoading(true);
        await axios.delete(`http://localhost:8000/api/loyalty-cards/${id}`);
        alert("Loyalty card deleted successfully!");
        fetchConfigurations();
      } catch (error) {
        console.error("Error deleting loyalty card:", error);
        alert("Failed to delete loyalty card.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleView = (config) => {
    setViewCard(config);
  };

  const closeViewModal = () => {
    setViewCard(null);
  };

  const resetForm = () => {
    setCardDetails({
      id: null,
      cardName: "",
      calculationType: "Point-wise",
      pointCalculationMode: "Threshold-wise",
      thresholdMethod: "per-threshold",
      pointsPerThreshold: "",
      pointsPerThresholdValue: "",
      singleThresholdAmount: "",
      singleThresholdPoints: "",
      singleThresholdPercentage: "",
      singleThresholdOperator: ">",
      percentagePerThreshold: "",
      percentagePerThresholdValue: "",
      ranges: [
        { minRange: "", maxRange: "", points: "", discountPercentage: "" },
      ],
    });
  };

  return (
    <div className="min-h-screen p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
            Loyalty Card Generator
      </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setActiveTab("create")}
              className={`px-4 py-2 rounded-lg font-medium ${activeTab === "create" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200"}`}
            >
              Create Card
            </button>
            <button
              onClick={() => setActiveTab("view")}
              className={`px-4 py-2 rounded-lg font-medium ${activeTab === "view" ? "bg-blue-600 text-white" : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200"}`}
            >
              View Cards
            </button>
          </div>
        </div>

        {activeTab === "create" && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-8 transition-all duration-300">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                {cardDetails.id ? "Edit Loyalty Card" : "Create New Loyalty Card"}
        </h3>
              {cardDetails.id && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Editing: {cardDetails.cardName}
                </p>
              )}
            </div>
            
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Loyalty Card Name
          </label>
          <input
            type="text"
            name="cardName"
            value={cardDetails.cardName}
            onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="e.g., Silver, Gold, Platinum"
          />
        </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Calculation Type
          </label>
          <select
            name="calculationType"
            value={cardDetails.calculationType}
            onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none" // hide native arrow
                    style={{ backgroundImage: 'none' }} // extra fallback
                    disabled
          >
            <option value="Point-wise">Point-wise</option>
                    {/* <option value="Percentage-wise">Percentage-wise</option> */}
          </select>
                </div>
        </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {cardDetails.calculationType === "Point-wise" 
                    ? "Point Calculation Method" 
                    : "Percentage Calculation Method"}
                </label>
                <div className="flex space-x-4 mb-4">
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      name="thresholdMethod"
                      value="per-threshold"
                      checked={cardDetails.thresholdMethod === "per-threshold"}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <span className="ml-2 text-gray-700 dark:text-gray-300">
                      {cardDetails.calculationType === "Point-wise" 
                        ? "Points per Threshold" 
                        : "Percentage per Threshold"}
                    </span>
            </label>
                  <label className="inline-flex items-center">
                    <input
                      type="radio"
                      name="thresholdMethod"
                      value="single-threshold"
                      checked={cardDetails.thresholdMethod === "single-threshold"}
              onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <span className="ml-2 text-gray-700 dark:text-gray-300">
                      Single Threshold Logic
                    </span>
                  </label>
          </div>

                {cardDetails.calculationType === "Point-wise" && cardDetails.thresholdMethod === "per-threshold" && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Threshold Amount (LKR)
              </label>
                <input
                  type="number"
                  name="pointsPerThreshold"
                  value={cardDetails.pointsPerThreshold}
                  onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="1000"
                  min={1}
                />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Points to Award
                        </label>
                <input
                  type="number"
                  name="pointsPerThresholdValue"
                  value={cardDetails.pointsPerThresholdValue}
                  onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="5"
                  min={1}
                />
                      </div>
              </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        <span className="font-semibold">Example:</span> For a 6000 LKR bill with 1 point per 1000 LKR, 
                        points = {calculatePoints(6000)}
                      </p>
                    </div>
            </div>
          )}

                {cardDetails.calculationType === "Percentage-wise" && cardDetails.thresholdMethod === "per-threshold" && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Threshold Amount (LKR)
            </label>
                <input
                  type="number"
                          name="percentagePerThreshold"
                          value={cardDetails.percentagePerThreshold || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          placeholder="1000"
                          min={1}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Percentage to Award
                        </label>
                        <div className="relative">
                <input
                  type="number"
                            name="percentagePerThresholdValue"
                            value={cardDetails.percentagePerThresholdValue || ''}
                            onChange={handleInputChange}
                            className="w-full pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="2"
                            min={0}
                            max={100}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500 dark:text-gray-400">
                            %
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        <span className="font-semibold">Example:</span> For a 6000 LKR bill with {cardDetails.percentagePerThresholdValue || '2'}% per {cardDetails.percentagePerThreshold || '1000'} LKR, 
                        discount = {cardDetails.percentagePerThresholdValue || '2'}% * {cardDetails.percentagePerThreshold ? Math.floor(6000 / cardDetails.percentagePerThreshold) : 6} = {cardDetails.percentagePerThresholdValue ? (cardDetails.percentagePerThreshold ? (Math.floor(6000 / cardDetails.percentagePerThreshold) * cardDetails.percentagePerThresholdValue) : 12) : 12}%
                      </p>
                    </div>
                  </div>
                )}

                {cardDetails.calculationType === "Point-wise" && cardDetails.thresholdMethod === "single-threshold" && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Condition
                        </label>
                        <select
                          name="singleThresholdOperator"
                          value={cardDetails.singleThresholdOperator}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value=">">Greater than</option>
                          <option value=">=">Greater than or equal to</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Threshold Amount (LKR)
                        </label>
                  <input
                    type="number"
                          name="singleThresholdAmount"
                          value={cardDetails.singleThresholdAmount || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          placeholder="5000"
                          min={1}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Points to Award
                        </label>
                  <input
                    type="number"
                          name="singleThresholdPoints"
                          value={cardDetails.singleThresholdPoints || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          placeholder="10"
                          min={1}
                        />
                      </div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        <span className="font-semibold">Example:</span> For a 6000 LKR bill, if sale is {cardDetails.singleThresholdOperator === '>' ? 'greater than' : 'equal or greater than'} {cardDetails.singleThresholdAmount || '5000'} LKR, give {cardDetails.singleThresholdPoints || '10'} points.
                      </p>
                    </div>
                  </div>
                )}

                {cardDetails.calculationType === "Percentage-wise" && cardDetails.thresholdMethod === "single-threshold" && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Condition
                        </label>
                        <select
                          name="singleThresholdOperator"
                          value={cardDetails.singleThresholdOperator}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value=">">Greater than</option>
                          <option value=">=">Greater than or equal to</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Threshold Amount (LKR)
                        </label>
                        <input
                          type="number"
                          name="singleThresholdAmount"
                          value={cardDetails.singleThresholdAmount || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                          placeholder="10000"
                          min={1}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Percentage to Award
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="singleThresholdPercentage"
                            value={cardDetails.singleThresholdPercentage || ''}
                            onChange={handleInputChange}
                            className="w-full pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            placeholder="5"
                            min={0}
                            max={100}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500 dark:text-gray-400">
                            %
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        <span className="font-semibold">Example:</span> For a 6000 LKR bill, if sale is {cardDetails.singleThresholdOperator === '>' ? 'greater than' : 'equal or greater than'} {cardDetails.singleThresholdAmount || '10000'} LKR, give {cardDetails.singleThresholdPercentage || '5'}% discount.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 flex justify-between">
              <button
                onClick={resetForm}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              >
                Reset Form
              </button>
              <div className="flex space-x-3">
                <button
                  onClick={() => setActiveTab("view")}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center"
                >
                  {isLoading && (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  {cardDetails.id ? "Update Card" : "Save Card"}
                </button>
          </div>
            </div>
          </div>
        )}

        {activeTab === "view" && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                Saved Loyalty Cards
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {configurations.length} card{configurations.length !== 1 ? 's' : ''} found
              </p>
      </div>

            {isLoading ? (
              <div className="p-8 flex justify-center">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
              </div>
            ) : configurations.length > 0 ? (
          <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Card Name
                  </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Calculation Mode
                  </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Reward Details
                  </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {configurations.map((config, index) => (
                      <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          <div className="flex items-center">
                            <span className="inline-block h-3 w-3 rounded-full bg-blue-500 mr-2"></span>
                      {config.card_name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                          {config.calculation_type}
                    </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {config.calculation_type === "Point-wise"
                            ? (config.threshold_method === "per-threshold"
                                ? "Points per Threshold"
                                : config.threshold_method === "single-threshold"
                                ? "Single Threshold"
                                : "-")
                            : config.calculation_type === "Percentage-wise"
                            ? (config.threshold_method === "per-threshold"
                                ? "Percentage per Threshold"
                                : config.threshold_method === "single-threshold"
                                ? "Single Threshold"
                                : "-")
                            : "-"}
                    </td>
                        <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                          {config.calculation_type === "Point-wise" && config.threshold_method === "per-threshold" ? (
                        <div>
                          {config.points_per_threshold_value} point
                              {parseInt(config.points_per_threshold_value) !== 1 ? "s" : ""} per {config.points_per_threshold} LKR
                            </div>
                          ) : config.calculation_type === "Point-wise" && config.threshold_method === "single-threshold" ? (
                            <div>
                              If sale {config.single_threshold_operator || '>'} {config.single_threshold_amount} LKR, give {config.single_threshold_points} points
                            </div>
                          ) : config.calculation_type === "Percentage-wise" && config.threshold_method === "per-threshold" ? (
                            <div>
                              {config.percentage_per_threshold_value}% per {config.percentage_per_threshold} LKR
                        </div>
                          ) : config.calculation_type === "Percentage-wise" && config.threshold_method === "single-threshold" ? (
                            <div>
                              If sale {config.single_threshold_operator || '>'} {config.single_threshold_amount} LKR, give {config.single_threshold_percentage}%
                            </div>
                      ) : (
                            <div>-</div>
                      )}
                    </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(config)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                      >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                      </button>
                      <button
                        onClick={() => handleEdit(config)}
                              className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"
                      >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                      </button>
                      <button
                        onClick={() => handleDelete(config.id)}
                              className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                      </button>
                          </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
            ) : (
              <div className="p-8 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No loyalty cards found</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new loyalty card.</p>
                <div className="mt-6">
                  <button
                    onClick={() => setActiveTab("create")}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    New Loyalty Card
                  </button>
                </div>
              </div>
            )}
        </div>
      )}

      {/* View Modal */}
      {viewCard && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Loyalty Card Details
            </h3>
                  <button
                    onClick={closeViewModal}
                    className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
            </div>
              </div>
              
              <div className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Card Name</p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">{viewCard.card_name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Calculation Type</p>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">{viewCard.calculation_type}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Calculation Method</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {viewCard.calculation_type === "Point-wise"
                      ? (viewCard.threshold_method === "per-threshold"
                          ? "Points per Threshold"
                          : viewCard.threshold_method === "single-threshold"
                          ? "Single Threshold Logic"
                          : "-")
                      : viewCard.calculation_type === "Percentage-wise"
                      ? (viewCard.threshold_method === "per-threshold"
                          ? "Percentage per Threshold"
                          : viewCard.threshold_method === "single-threshold"
                          ? "Single Threshold Logic"
                          : "-")
                      : "-"}
                  </p>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Reward Details</p>
                  {viewCard.calculation_type === "Point-wise" && viewCard.threshold_method === "per-threshold" ? (
                    <div className="text-sm text-gray-900 dark:text-white">
                      <p>{viewCard.points_per_threshold_value} point
                      {parseInt(viewCard.points_per_threshold_value) !== 1 ? "s" : ""} per {viewCard.points_per_threshold} LKR</p>
                    </div>
                  ) : viewCard.calculation_type === "Point-wise" && viewCard.threshold_method === "single-threshold" ? (
                    <div className="text-sm text-gray-900 dark:text-white">
                      <p>If sale {viewCard.single_threshold_operator || '>'} {viewCard.single_threshold_amount} LKR, give {viewCard.single_threshold_points} points</p>
                    </div>
                  ) : viewCard.calculation_type === "Percentage-wise" && viewCard.threshold_method === "per-threshold" ? (
                    <div className="text-sm text-gray-900 dark:text-white">
                      <p>{viewCard.percentage_per_threshold_value}% per {viewCard.percentage_per_threshold} LKR</p>
                    </div>
                  ) : viewCard.calculation_type === "Percentage-wise" && viewCard.threshold_method === "single-threshold" ? (
                    <div className="text-sm text-gray-900 dark:text-white">
                      <p>If sale {viewCard.single_threshold_operator || '>'} {viewCard.single_threshold_amount} LKR, give {viewCard.single_threshold_percentage}%</p>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-900 dark:text-white">-</div>
                  )}
              </div>

                {Array.isArray(viewCard.ranges) && viewCard.ranges.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Ranges</p>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Min Range</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Max Range</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              {viewCard.calculation_type === "Point-wise" ? "Points" : "Percentage"}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {viewCard.ranges.map((range, idx) => (
                            <tr key={idx}>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">{range.min_range || range.minRange} LKR</td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">{range.max_range || range.maxRange} LKR</td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {viewCard.calculation_type === "Point-wise"
                          ? `${range.points} points`
                                  : `${range.discount_percentage || range.discountPercentage}%`}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end">
              <button
                onClick={closeViewModal}
                  className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default GenerateLoyaltyCard;