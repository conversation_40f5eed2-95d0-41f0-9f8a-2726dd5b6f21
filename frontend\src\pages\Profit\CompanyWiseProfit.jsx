import React, { useState, useEffect } from "react";
import axios from "axios";
import Select from "react-select";
import ReportTable from "../../components/reports/ReportTable";
import * as XLSX from "xlsx";
import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";

const CompanyWiseProfit = () => {
  const [companies, setCompanies] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [reportData, setReportData] = useState([]);
  const [itemDetails, setItemDetails] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [expandedRow, setExpandedRow] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");

  const fetchCompanies = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/companies");
      const companyOptions = response.data.map((company) => ({
        value: company.company_name || company.name,
        label: company.company_name || company.name,
      }));
      setCompanies(companyOptions);
    } catch (error) {
      console.error("Error fetching companies:", error.response || error);
      setError("Failed to load companies. Please try again.");
    }
  };
  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchReportData = async () => {
    // Remove early return to allow fetching all companies when no company is selected
    if (fromDate && toDate && new Date(fromDate) > new Date(toDate)) {
      setError("From date cannot be later than To date.");
      return;
    }

    try {
      setLoading(true);
      setError("");
      const response = await axios.get(
        "http://127.0.0.1:8000/api/sales/company-wise-profit-report",
        {
          params: {
            companyName: selectedCompany ? selectedCompany.value : "",
            fromDate: fromDate || undefined,
            toDate: toDate || undefined,
          },
        }
      );

      console.log("Raw API reportData:", response.data.reportData);

      if (response.data?.reportData) {
        const processedData = response.data.reportData.map((item) => ({
          companyName: item.companyName || item.company_name,
          totalQuantity: item.totalQuantity || item.quantity || 0,
          totalCostPrice: item.totalCostPrice || item.total_cost || 0,
          totalSellingPrice: item.totalSellingPrice || item.total_sales || 0,
          totalProfit: item.totalProfit || item.profit || 0,
          profit_percentage:
            item.profitPercentage || item.profit_percentage || "0.00%",
          items: item.items || [],
        }));

        console.log("Processed reportData:", processedData);

        const items = processedData.flatMap((item) => item.items);

        setReportData(processedData);
        setItemDetails(items);
        setSummary({
          totalQuantityAll: response.data.summary?.totalQuantityAll || 0,
          totalCostPriceAll: response.data.summary?.totalCostPriceAll || 0,
          totalSellingPriceAll:
            response.data.summary?.totalSellingPriceAll || 0,
          totalProfitAll: response.data.summary?.totalProfitAll || 0,
          totalProfitPercentage:
            response.data.summary?.averageProfitPercentageAll || "0.00%",
        });

        if (processedData.length === 0) {
          setError(
            selectedCompany
              ? `No data found for company "${selectedCompany.label}"${fromDate && toDate ? ` between ${fromDate} and ${toDate}` : ""}.`
              : `No data found for all companies${fromDate && toDate ? ` between ${fromDate} and ${toDate}` : ""}.`
          );
        }
      } else {
        setReportData([]);
        setItemDetails([]);
        setSummary({});
        setError(
          selectedCompany
            ? `No data found for company "${selectedCompany.label}"${fromDate && toDate ? ` between ${fromDate} and ${toDate}` : ""}.`
            : `No data found for all companies${fromDate && toDate ? ` between ${fromDate} and ${toDate}` : ""}.`
        );
      }
    } catch (error) {
      console.error("Error fetching report data:", error.response || error);
      const errorMessage =
        error.response?.data?.error ||
        "Failed to load report data. Please check the API and try again.";
      setError(errorMessage);
      setReportData([]);
      setItemDetails([]);
      setSummary({});
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReportData();
  }, [selectedCompany, fromDate, toDate]);

  useEffect(() => {
    fetchCompanies();
  }, []);

  useEffect(() => {
    fetchReportData();
  }, [selectedCompany, fromDate, toDate]);

  const toggleRow = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  // Filtered data based on search query and company name
  const filteredData = reportData.filter((row) => {
    if (!searchQuery.trim()) {
      return true;
    }
    return (
      row.companyName &&
      row.companyName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const exportToExcel = () => {
    const wb = XLSX.utils.book_new();
    const summaryData = filteredData.map((row) => ({
      Company: row.companyName,
      "Total Quantity": row.totalQuantity,
      "Total Cost": row.totalCostPrice,
      "Total Sales": row.totalSellingPrice,
      "Total Profit": row.totalProfit,
      "Profit %": row.profit_percentage,
    }));
    const summaryWs = XLSX.utils.json_to_sheet([
      {
        Report: `Company Wise Profit Report${fromDate && toDate ? ` (${fromDate} to ${toDate})` : ""}`,
      },
      {},
      ...summaryData,
    ]);
    XLSX.utils.book_append_sheet(wb, summaryWs, "Company Summary");
    const itemsWs = XLSX.utils.json_to_sheet(itemDetails);
    XLSX.utils.book_append_sheet(wb, itemsWs, "Item Details");
    XLSX.writeFile(
      wb,
      `Company_Wise_Profit_Report${fromDate && toDate ? `_${fromDate}_to_${toDate}` : ""}.xlsx`
    );
  };

  const exportToPDF = () => {
    const doc = new jsPDF();
    doc.text(
      `Company Wise Profit Report${fromDate && toDate ? ` (${fromDate} to ${toDate})` : ""}`,
      10,
      10
    );
    doc.autoTable({
      head: [
        [
          "Company",
          "Total Quantity",
          "Total Cost",
          "Total Sales",
          "Total Profit",
          "Profit %",
        ],
      ],
      body: filteredData.map((row) => [
        row.companyName,
        row.totalQuantity,
        row.totalCostPrice,
        row.totalSellingPrice,
        row.totalProfit,
        row.profit_percentage,
      ]),
      startY: 20,
    });
    if (itemDetails.length > 0) {
      doc.text("Item Details", 10, doc.lastAutoTable.finalY + 10);
      doc.autoTable({
        head: [
          [
            "Product Name",
            "Quantity",
            "Unit Price",
            "Total Cost",
            "Total Sales",
            "Profit",
          ],
        ],
        body: itemDetails.map((item) => [
          item.product_name || item.productName || "",
          item.quantity || 0,
          item.unit_price || item.unitPrice || 0,
          item.total_cost || item.totalCost || 0,
          item.total_sales || item.totalSales || 0,
          item.profit || 0,
        ]),
        startY: doc.lastAutoTable.finalY + 20,
      });
    }
    doc.save(
      `Company_Wise_Profit_Report${fromDate && toDate ? `_${fromDate}_to${toDate}` : ""}.pdf`
    );
  };

  return (
    <div className="flex flex-col min-h-screen p-4 bg-transparent">
      <div className="p-2 text-center text-white bg-blue-600 rounded-lg">
        <h1 className="text-2xl font-bold">Company Wise Profit Report</h1>
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
        <div className="flex-1 min-w-[200px]">
          <label className="flex flex-col">
            <span className="mb-1 font-medium">Select Company:</span>
            <Select
              options={companies}
              value={selectedCompany}
              onChange={setSelectedCompany}
              placeholder="Type to search companies..."
              isClearable
              className="text-sm"
              classNamePrefix="react-select"
              styles={{
                control: (base) => ({
                  ...base,
                  borderColor: "#d1d5db",
                  borderRadius: "0.375rem",
                  padding: "0.25rem",
                  boxShadow: "none",
                  "&:hover": {
                    borderColor: "#3b82f6",
                  },
                }),
                menu: (base) => ({
                  ...base,
                  zIndex: 9999,
                  borderRadius: "0.375rem",
                  marginTop: "0.25rem",
                }),
                option: (base, { isFocused, isSelected }) => ({
                  ...base,
                  backgroundColor: isSelected
                    ? "#3b82f6"
                    : isFocused
                      ? "#e5e7eb"
                      : "white",
                  color: isSelected ? "white" : "#1f2937",
                  padding: "0.5rem 1rem",
                }),
              }}
            />
          </label>
        </div>

        <div className="flex-1 min-w-[200px]">
          <label className="flex flex-col">
            <span className="mb-1 font-semibold">From Date:</span>
            <input
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              className="p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </label>
        </div>
        <div className="flex-1 min-w-[200px]">
          <label className="flex flex-col">
            <span className="mb-1 font-semibold">To Date:</span>
            <input
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              className="p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </label>
        </div>

        <div className="flex-1 min-w-[200px]">
          <label className="flex flex-col">
            <span className="mb-1 font-semibold">Search Company:</span>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search company name..."
              className="p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </label>
        </div>

        <div className="flex-1 min-w-[200px]">
          {loading && <p className="text-blue-500">Loading...</p>}
          {error && <p className="text-red-500">{error}</p>}
        </div>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={exportToExcel}
            className="px-4 py-2 text-white transition duration-300 bg-green-500 rounded-lg hover:bg-green-600"
            disabled={!reportData.length}
          >
            Export to Excel
          </button>
          <button
            onClick={exportToPDF}
            className="px-4 py-2 text-white transition duration-300 bg-red-500 rounded-lg hover:bg-red-600"
            disabled={!reportData.length}
          >
            Export to PDF
          </button>
          <button
            onClick={() => window.print()}
            className="px-4 py-2 text-white transition duration-300 bg-blue-500 rounded-lg hover:bg-blue-600"
            disabled={!reportData.length}
          >
            Print
          </button>
        </div>
      </div>

      {filteredData.length > 0 ? (
        <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                <tr>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">
                    Company Name
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Total Quantity
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Total Cost
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Total Sales
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Total Profit
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Profit %
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap"></th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
                {filteredData.map((row, index) => (
                  <React.Fragment key={row.companyName}>
                    <tr
                      className={`hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors ${
                        expandedRow === index
                          ? "bg-blue-50 dark:bg-slate-700"
                          : ""
                      }`}
                    >
                      <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">
                        <button
                          onClick={() => toggleRow(index)}
                          className="hover:underline focus:outline-none"
                        >
                          {row.companyName}
                        </button>
                      </td>
                      <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300 whitespace-nowrap">
                        {row.totalQuantity}
                      </td>
                      <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300 whitespace-nowrap">
                        {row.totalCostPrice}
                      </td>
                      <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300 whitespace-nowrap">
                        {row.totalSellingPrice}
                      </td>
                      <td className="px-4 py-3 font-semibold text-right text-gray-800 dark:text-white whitespace-nowrap">
                        {row.totalProfit}
                      </td>
                      <td className="px-4 py-3 text-right text-gray-600 dark:text-gray-300 whitespace-nowrap">
                        {row.profit_percentage}
                      </td>
                      <td className="px-4 py-3 text-right whitespace-nowrap">
                        <button
                          onClick={() => toggleRow(index)}
                          title={
                            expandedRow === index
                              ? "Collapse Details"
                              : "Expand Details"
                          }
                          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white focus:outline-none"
                        >
                          {expandedRow === index ? (
                            <FiChevronUp size={18} />
                          ) : (
                            <FiChevronDown size={18} />
                          )}
                        </button>
                      </td>
                    </tr>
                    {expandedRow === index && (
                      <tr className="bg-gray-50 dark:bg-slate-900/30">
                        <td colSpan={7} className="px-4 py-4 md:px-6 md:py-4">
                          <div className="p-3 border border-gray-200 rounded-md dark:border-slate-700">
                            <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase dark:text-gray-400">
                              Item Details ({row.items?.length || 0})
                            </h4>
                            {row.items && row.items.length > 0 ? (
                              <div className="overflow-x-auto max-h-60">
                                <ReportTable
                                  data={row.items}
                                  columns={[
                                    {
                                      header: "Product Name",
                                      field: "product_name",
                                    },
                                    { header: "Quantity", field: "quantity" },
                                    {
                                      header: "Unit Price",
                                      field: "unit_price",
                                    },
                                    {
                                      header: "Total Cost",
                                      field: "total_cost",
                                    },
                                    {
                                      header: "Total Sales",
                                      field: "total_sales",
                                    },
                                    { header: "Profit", field: "profit" },
                                  ]}
                                />
                              </div>
                            ) : (
                              <p className="text-sm text-center text-gray-500 dark:text-gray-400">
                                No item details available.
                              </p>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
          <div className="p-4 mt-4 text-center bg-transparent rounded-lg shadow-lg">
            <h2 className="mb-4 text-xl font-bold">Summary</h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
              <div className="p-4 rounded-lg bg-cyan-800">
                <p className="text-sm text-cyan-500">Total Quantity</p>
                <p className="text-2xl font-bold text-cyan-300">
                  {summary.totalQuantityAll || 0}
                </p>
              </div>
              <div className="p-4 rounded-lg bg-lime-800">
                <p className="text-sm text-lime-500">Total Cost</p>
                <p className="text-2xl font-bold text-lime-300">
                  LKR {summary.totalCostPriceAll || 0}
                </p>
              </div>
              <div className="p-4 rounded-lg bg-cyan-800">
                <p className="text-sm text-cyan-500">Total Sales</p>
                <p className="text-2xl font-bold text-cyan-300">
                  LKR {summary.totalSellingPriceAll || 0}
                </p>
              </div>
              <div className="p-4 rounded-lg bg-rose-800">
                <p className="text-sm text-pink-500">Total Profit</p>
                <p className="text-2xl font-bold text-pink-300">
                  LKR {summary.totalProfitAll || 0}
                </p>
              </div>
              <div className="p-4 rounded-lg bg-fuchsia-800">
                <p className="text-sm text-fuchsia-500">Profit Margin</p>
                <p className="text-2xl font-bold text-fuchsia-300">
                  {summary.totalProfitPercentage || "0.00%"}
                </p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        !loading &&
        error && <p className="text-center text-gray-500">{error}</p>
      )}
    </div>
  );
};

export default CompanyWiseProfit;
