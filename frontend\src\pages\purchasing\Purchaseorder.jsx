import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import {
  FiChevronDown,
  FiChevronUp,
  FiEdit,
  FiTrash2,
  FiPrinter,
} from "react-icons/fi";
import Select from "react-select";
import PrintablePurchaseOrder from "./PrintablePurchaseOrder";

const API_BASE_URL = "http://127.0.0.1:8000/api";

// Custom option component for enhanced display
const CustomOption = ({
  innerRef,
  innerProps,
  data,
  isSelected,
  isFocused,
}) => (
  <div
    ref={innerRef}
    {...innerProps}
    className={`px-4 py-2 cursor-pointer ${
      isSelected
        ? "bg-blue-600 text-white"
        : isFocused
          ? "bg-blue-100 dark:bg-blue-600 text-blue-900 dark:text-white"
          : "hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200"
    }`}
  >
    <div className="font-medium">{data.label}</div>
    <div className="text-xs text-gray-500 dark:text-gray-400">
      {data.description}
    </div>
  </div>
);

// Custom styles for react-select
const customSelectStyles = {
  control: (provided, state) => ({
    ...provided,
    borderColor: state.isFocused ? "#2563eb" : "#d1d5db",
    boxShadow: state.isFocused ? "0 0 0 1px #2563eb" : "none",
    minHeight: "42px",
    borderRadius: "0.375rem",
    backgroundColor: state.isDisabled ? "#f3f4f6" : "white",
    "&:hover": {
      borderColor: "#2563eb",
    },
    fontSize: "0.875rem",
    color: "#111827",
    ...(state.selectProps.isDarkMode && {
      borderColor: state.isFocused ? "#60a5fa" : "#4b5563",
      backgroundColor: state.isDisabled ? "#374151" : "#1f2937",
      color: "#f9fafb",
    }),
  }),
  menu: (provided, state) => ({
    ...provided,
    zIndex: 9999,
    fontSize: "0.875rem",
    backgroundColor: "white",
    border: "1px solid #d1d5db",
    borderRadius: "0.375rem",
    ...(state.selectProps.isDarkMode && {
      backgroundColor: "#1f2937",
      border: "1px solid #4b5563",
    }),
  }),
  option: () => ({
    // Custom styling handled by CustomOption component
  }),
  placeholder: (provided, state) => ({
    ...provided,
    color: "#6b7280",
    ...(state.selectProps.isDarkMode && {
      color: "#9ca3af",
    }),
  }),
  singleValue: (provided, state) => ({
    ...provided,
    color: "#111827",
    ...(state.selectProps.isDarkMode && {
      color: "#f9fafb",
    }),
  }),
};

// PrintableOrdersReport component
const PrintableOrdersReport = ({ orders, formatCurrency }) => {
  return (
    <div className="p-8 bg-white border border-gray-300 rounded-lg printable-orders-report dark:bg-white dark:border-gray-300">
      <div className="mb-6 text-center">
        <h1 className="text-2xl font-bold text-gray-900">
          Purchase Orders Summary
        </h1>
        <p className="text-sm text-gray-500">
          Generated on {new Date().toLocaleString()}
        </p>
      </div>
      <table className="min-w-full text-sm border border-gray-300">
        <thead className="text-white bg-blue-900">
          <tr>
            <th className="px-4 py-3 font-semibold text-left border-r border-gray-300">
              Order ID
            </th>
            <th className="px-4 py-3 font-semibold text-left border-r border-gray-300">
              Supplier
            </th>
            <th className="px-4 py-3 font-semibold text-right border-r border-gray-300">
              Total
            </th>
            <th className="px-4 py-3 font-semibold text-left">Items</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order, index) => (
            <tr
              key={order.id}
              className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}
            >
              <td className="px-4 py-3 border-r border-gray-300">
                #{order.id}
              </td>
              <td className="px-4 py-3 border-r border-gray-300">
                <div className="font-semibold">
                  {order.supplier?.supplier_name || order.contact_name || "N/A"}
                </div>
                <div className="text-xs text-gray-500">
                  {order.phone || "N/A"}
                </div>
              </td>
              <td className="px-4 py-3 text-right border-r border-gray-300">
                {formatCurrency(order.total)}
              </td>
              <td className="px-4 py-3">
                {Array.isArray(order.orderItems) &&
                order.orderItems.length > 0 ? (
                  <ul className="text-xs list-disc list-inside">
                    {order.orderItems.map((item) => (
                      <li key={item.id}>
                        {(item.product?.product_name || item.description) +
                          ` (Qty: ${item.qty}, Unit Price: ${formatCurrency(
                            item.unit_price
                          )})`}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <span className="text-gray-500">No items</span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div className="mt-6 text-right">
        <p className="text-sm font-semibold">
          Total Across All Orders:{" "}
          {formatCurrency(
            orders.reduce((sum, order) => sum + Number(order.total || 0), 0)
          )}
        </p>
      </div>
    </div>
  );
};

const PurchaseOrder = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [products, setProducts] = useState([]);
  const [selectedSupplierId, setSelectedSupplierId] = useState("");
  const [supplier, setSupplier] = useState({
    contactName: "",
    address: "",
    phone: "",
  });
  const [items, setItems] = useState([]);
  const [newItem, setNewItem] = useState({
    productId: null,
    variantId: null,
    description: "",
    qty: 1,
    unitPrice: 0,
    total: 0,
    batchNumber: "",
    expiryDate: "",
    barcode: "",
  });
  const [errors, setErrors] = useState({});
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showOrders, setShowOrders] = useState(false);
  const [expandedRow, setExpandedRow] = useState(null);
  const [editingOrder, setEditingOrder] = useState(null);
  const [printOrderData, setPrintOrderData] = useState(null);
  const [printAllOrders, setPrintAllOrders] = useState(false);
  const [productSearchTerm, setProductSearchTerm] = useState("");

  const newItemProductSelectRef = useRef(null);
  const newItemQtyRef = useRef(null);
  const newItemUnitPriceRef = useRef(null);

  // Fetch suppliers and products
  useEffect(() => {
    const fetchSuppliersAndProducts = async () => {
      try {
        const [suppliersResponse, productsResponse] = await Promise.all([
          axios.get(`${API_BASE_URL}/suppliers`),
          axios.get(`${API_BASE_URL}/products`),
        ]);
        setSuppliers(suppliersResponse.data || []);

        // Generate filtered product options with variants (similar to POS form)
        const productsData = productsResponse.data.data || [];
        const productOptions = [];

        productsData.forEach((product) => {
          if (product.variants && product.variants.length > 0) {
            // Product has variants - create option for each variant
            product.variants.forEach((variant) => {
              const batchInfo = variant.batch_number
                ? ` (Batch: ${variant.batch_number})`
                : "";
              const expiryInfo = variant.expiry_date
                ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
                : "";

              const variantBuyingCost = parseFloat(
                variant.buying_cost || product.buying_cost || 0
              );
              console.log(
                `Creating variant option for ${product.product_name} batch ${variant.batch_number}:`,
                {
                  variant_buying_cost: variant.buying_cost,
                  product_buying_cost: product.buying_cost,
                  final_buying_cost: variantBuyingCost,
                  batch_number: variant.batch_number,
                }
              );

              const option = {
                ...product,
                ...variant,
                // Use variant-specific values
                product_id: product.product_id,
                variant_id:
                  variant.product_variant_id ||
                  variant.variant_id ||
                  variant.id ||
                  `${product.product_id}-batch-${variant.batch_number || "nobatch"}`,
                display_name: `${product.product_name}${batchInfo}${expiryInfo}`,
                stock: parseFloat(variant.opening_stock_quantity || 0),
                buying_cost: variantBuyingCost,
                sales_price: parseFloat(variant.sales_price || 0),
                mrp: parseFloat(variant.mrp || 0),
                batch_number: variant.batch_number,
                expiry_date: variant.expiry_date,
                barcode: variant.barcode || "N/A",
                cabinet: variant.cabinet,
                row: variant.row,
                minimum_stock_quantity: variant.minimum_stock_quantity || 0,
              };

              productOptions.push(option);
            });
          } else {
            // No variants - add product as is
            const option = {
              ...product,
              variant_id: null,
              display_name: product.product_name,
              stock: parseFloat(product.opening_stock_quantity || 0),
              buying_cost: parseFloat(product.buying_cost || 0),
              sales_price: parseFloat(product.sales_price || 0),
              mrp: parseFloat(product.mrp || 0),
              batch_number: null,
              expiry_date: null,
              barcode: "N/A",
            };

            productOptions.push(option);
          }
        });

        setProducts(productOptions);
      } catch (error) {
        setError("Failed to load suppliers or products");
      }
    };
    fetchSuppliersAndProducts();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`);
      setOrders(response.data || []);
    } catch (error) {
      setError("Failed to load orders: " + error.message);
    }
  };

  // Fetch purchase orders on component mount
  useEffect(() => {
    fetchOrders();
  }, []);

  // Update supplier details
  useEffect(() => {
    if (selectedSupplierId && !editingOrder) {
      const selected = suppliers.find(
        (sup) => sup.id === parseInt(selectedSupplierId)
      );
      if (selected) {
        setSupplier({
          contactName: selected.supplier_name,
          address: selected.address,
          phone: selected.contact,
        });
      }
    } else if (!selectedSupplierId && !editingOrder) {
      setSupplier({ contactName: "", address: "", phone: "" });
    }
  }, [selectedSupplierId, suppliers, editingOrder]);

  const handleSupplierChange = (e) => {
    setSelectedSupplierId(e.target.value);
  };

  const handleNewItemProductSelect = (selectedOption) => {
    const productId = selectedOption ? selectedOption.value : null;
    const variantId = selectedOption ? selectedOption.variantId : null;

    console.log("Selected option:", selectedOption);
    console.log("Looking for productId:", productId, "variantId:", variantId);

    // Find the exact product variant/batch that was selected
    const product = products.find((p) => {
      if (variantId && p.variant_id) {
        // If we have a variant ID, match exactly by variant ID
        const match = p.variant_id === variantId;
        if (match) {
          console.log("Found matching product by variant_id:", p);
        }
        return match;
      } else if (!variantId && !p.variant_id) {
        // If no variant ID, match products without variants
        const match = p.product_id === productId;
        if (match) {
          console.log("Found matching product by product_id:", p);
        }
        return match;
      }
      return false;
    });

    const unitPrice = product ? parseFloat(product.buying_cost) || 0 : 0;
    const qty = newItem.qty || 1;
    const total = qty * unitPrice;

    setNewItem({
      ...newItem,
      productId,
      variantId,
      description: product ? product.display_name || product.product_name : "",
      unitPrice,
      total,
      batchNumber: product ? product.batch_number || "" : "",
      expiryDate: product ? product.expiry_date || "" : "",
      barcode: product ? product.barcode || "" : "",
    });
    setErrors((prev) => ({ ...prev, newItemDescription: undefined }));
    // Move focus to quantity input after selection
    if (productId) {
      setTimeout(() => {
        newItemQtyRef.current?.focus();
        newItemQtyRef.current?.select();
      }, 0);
    }
  };

  const handleNewItemInputChange = (e, field) => {
    const value = e.target.value;
    setNewItem((prev) => {
      const updatedItem = {
        ...prev,
        [field]:
          field === "qty" || field === "unitPrice"
            ? value === ""
              ? ""
              : parseFloat(value) || 0
            : value,
      };
      updatedItem.total = (updatedItem.qty || 0) * (updatedItem.unitPrice || 0);
      return updatedItem;
    });
    setErrors((prev) => ({
      ...prev,
      [`newItem${field.charAt(0).toUpperCase() + field.slice(1)}`]: undefined,
    }));
  };

  const handleAddNewItem = () => {
    const { productId, description, qty, unitPrice } = newItem;
    const newErrors = {};
    if (!productId || !description)
      newErrors.newItemDescription = "Product is required";
    if (qty === "" || qty <= 0)
      newErrors.newItemQty = "Quantity must be positive";
    if (unitPrice === "" || unitPrice < 0)
      newErrors.newItemUnitPrice = "Unit Price must be non-negative";

    if (Object.keys(newErrors).length > 0) {
      setErrors((prev) => ({ ...prev, ...newErrors }));
      alert("Please fix validation errors.");
      return;
    }

    const newItemToAdd = {
      productId,
      variantId: newItem.variantId,
      description,
      qty,
      unitPrice,
      total: qty * unitPrice,
      batchNumber: newItem.batchNumber,
      expiryDate: newItem.expiryDate,
      barcode: newItem.barcode,
    };

    setItems([...items, newItemToAdd]);
    setNewItem({
      productId: null,
      variantId: null,
      description: "",
      qty: 1,
      unitPrice: 0,
      total: 0,
      batchNumber: "",
      expiryDate: "",
      barcode: "",
    });
    setErrors({});
    newItemProductSelectRef.current?.clearValue();
    setTimeout(() => newItemProductSelectRef.current?.focus(), 0);
  };

  const handleItemChange = (index, e) => {
    const { name, value } = e.target;
    const updatedItems = [...items];
    updatedItems[index][name] =
      name === "qty" || name === "unitPrice"
        ? value === ""
          ? ""
          : parseFloat(value) || 0
        : value;
    updatedItems[index].total =
      updatedItems[index].qty * updatedItems[index].unitPrice;
    setItems(updatedItems);
  };

  const removeItem = (index) => {
    const updatedItems = items.filter((_, idx) => idx !== index);
    setItems(updatedItems);
  };

  const calculateTotal = () => {
    return items.reduce((sum, item) => sum + (item.total || 0), 0);
  };

  const handleSubmit = async () => {
    setError(null);
    setSuccess(null);

    if (!selectedSupplierId) {
      setError("Please select a supplier");
      alert("Please select a supplier");
      return;
    }

    if (items.length === 0) {
      setError("At least one item is required");
      alert("At least one item is required");
      return;
    }

    const data = {
      supplierId: selectedSupplierId,
      contactName: supplier.contactName,
      phone: supplier.phone,
      address: supplier.address,
      items: items.map((item) => ({
        productId: item.productId,
        description: item.description,
        qty: item.qty,
        unitPrice: item.unitPrice,
        total: item.total,
      })),
    };

    try {
      let response;
      if (editingOrder) {
        response = await axios.put(
          `${API_BASE_URL}/purchase-orders/${editingOrder.id}`,
          data
        );
      } else {
        response = await axios.post(`${API_BASE_URL}/purchase-orders`, data);
      }
      setSuccess(response.data.message);
      await fetchOrders();
      resetForm();
      alert(
        editingOrder
          ? "Purchase order updated successfully!"
          : "Purchase order created successfully!"
      );
    } catch (err) {
      const errorMessage =
        err.response?.status === 422
          ? "Validation failed: " + JSON.stringify(err.response.data.errors)
          : "Failed to submit order: " + err.message;
      setError(errorMessage);
      alert(errorMessage);
    }
  };

  const resetForm = () => {
    setSelectedSupplierId("");
    setSupplier({ contactName: "", address: "", phone: "" });
    setItems([]);
    setNewItem({
      productId: null,
      variantId: null,
      description: "",
      qty: 1,
      unitPrice: 0,
      total: 0,
      batchNumber: "",
      expiryDate: "",
      barcode: "",
    });
    setEditingOrder(null);
    setErrors({});
  };

  const handleEdit = (order) => {
    console.log("handleEdit called with order:", order);
    setEditingOrder(order);
    setSelectedSupplierId(order.supplier_id.toString());
    setSupplier({
      contactName: order.contact_name,
      address: order.address,
      phone: order.phone,
    });
    setItems(
      (order.orderItems || []).map((item) => ({
        productId: item.product_id,
        description: item.description,
        qty: item.qty,
        unitPrice: Number(item.unit_price),
        total: Number(item.total),
      }))
    );
    setShowOrders(false);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this order?")) return;
    try {
      await axios.delete(`${API_BASE_URL}/purchase-orders/${id}`);
      await fetchOrders();
      setSuccess("Purchase order deleted successfully");
      alert("Purchase order deleted successfully");
    } catch (error) {
      setError("Failed to delete order: " + error.message);
      alert("Failed to delete order");
    }
  };

  const handleViewPrint = (order) => {
    console.log("handleViewPrint called with order:", order);
    setPrintOrderData({
      supplier: {
        name: order.supplier?.supplier_name || order.contact_name || "N/A",
        address: order.address || "N/A",
        phone: order.phone || "N/A",
      },
      items: order.orderItems.map((item) => ({
        id: item.id,
        description: item.description || "N/A",
        qty: item.qty || 0,
        unitPrice: Number(item.unit_price) || 0,
        total: Number(item.total) || 0,
      })),
      footerDetails: {
        approvedBy: "System",
        nextApprovalTo: "",
        dateTime: new Date(
          order.updated_at || order.created_at || Date.now()
        ).toLocaleString(),
      },
      total: Number(order.total) || 0,
      order: {
        no: order.id.toString(),
        date: new Date(order.created_at || Date.now()).toLocaleDateString(),
        time: new Date(order.created_at || Date.now()).toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      },
    });
  };

  const closePrintModal = () => {
    setPrintOrderData(null);
  };

  const toggleRow = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  const formatCurrency = (amount) => {
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) {
      return "LKR 0.00";
    }
    return new Intl.NumberFormat("en-LK", {
      style: "currency",
      currency: "LKR",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numericAmount);
  };

  const handlePrintOrders = () => {
    if (orders.length === 0) {
      alert("No orders to print.");
      return;
    }
    setPrintAllOrders(true);
    setTimeout(() => {
      window.print();
      setPrintAllOrders(false);
    }, 100);
  };

  const getFieldOrder = () => {
    return [
      { ref: newItemQtyRef, name: "newItemQty", type: "input" },
      { ref: newItemUnitPriceRef, name: "newItemUnitPrice", type: "input" },
    ];
  };

  const handleKeyDown = (e, ref) => {
    if (e.key !== "Enter" || e.shiftKey) return;
    e.preventDefault();

    const fields = getFieldOrder();
    const currentFieldIndex = fields.findIndex((field) => field.ref === ref);
    if (currentFieldIndex === -1) return;

    const currentField = fields[currentFieldIndex];

    // If on unit price field, add the item
    if (currentField.name === "newItemUnitPrice") {
      handleAddNewItem();
      return;
    }

    // Move to the next field
    for (let i = currentFieldIndex + 1; i < fields.length; i++) {
      const nextField = fields[i];
      if (nextField.ref.current) {
        nextField.ref.current.focus();
        if (nextField.type === "input") nextField.ref.current.select?.();
        break;
      }
    }
  };

  const handleProductSelectKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      // Allow react-select to handle Enter for selecting an option
      // If a product is selected, handleNewItemProductSelect will move focus to quantity
      if (newItem.productId) {
        e.preventDefault();
        setTimeout(() => {
          newItemQtyRef.current?.focus();
          newItemQtyRef.current?.select();
        }, 0);
      }
    }
    // Allow arrow keys to work natively in react-select for navigating options
  };

  const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
  const filteredProductOptions = products
    .filter((product) => {
      const search = productSearchTerm.toLowerCase();
      if (startsWithOnly) {
        return (
          product.display_name.toLowerCase().startsWith(search) ||
          product.product_name.toLowerCase().startsWith(search) ||
          (product.batch_number && product.batch_number.toLowerCase().startsWith(search)) ||
          (product.barcode && product.barcode !== "N/A" && product.barcode.toLowerCase().startsWith(search))
        );
      } else {
        return (
          product.display_name.toLowerCase().includes(search) ||
          product.product_name.toLowerCase().includes(search) ||
          (product.batch_number && product.batch_number.toLowerCase().includes(search)) ||
          (product.barcode && product.barcode !== "N/A" && product.barcode.toLowerCase().includes(search))
        );
      }
    })
    .map((p) => ({
      value: p.product_id,
      variantId: p.variant_id,
      label: p.display_name,
      description: `Stock: ${p.stock || 0} | Cost: LKR ${p.buying_cost?.toFixed(2) || "0.00"}${
        p.batch_number ? ` | Batch: ${p.batch_number}` : ""
      }${p.barcode && p.barcode !== "N/A" ? ` | Barcode: ${p.barcode}` : ""}`,
    }));

  return (
    <div className="flex flex-col min-h-screen p-4 bg-transparent">
      <style>
        {`
          @media print {
            body * {
              visibility: hidden;
            }
            .printable-purchase-order, .printable-purchase-order *, 
            .printable-orders-report, .printable-orders-report * {
              visibility: visible;
            }
            .printable-purchase-order, .printable-orders-report {
              position: fixed;
              left: 0;
              top: 0;
              width: 100%;
              height: auto;
              margin: 0;
              padding: 20mm;
              box-sizing: border-box;
              background: white;
              color: black;
            }
            .printable-purchase-order table, .printable-orders-report table {
              border-collapse: collapse;
              width: 100%;
            }
            .printable-purchase-order th, .printable-purchase-order td,
            .printable-orders-report th, .printable-orders-report td {
              border: 1px solid #d1d5db;
              padding: 8px;
              color: #1f2937;
            }
            .printable-purchase-order th, .printable-orders-report th {
              background-color: #1e40af;
              color: white;
            }
            .dark .printable-purchase-order, .dark .printable-orders-report {
              background: white;
              color: black;
            }
            .dark .printable-purchase-order th, .dark .printable-purchase-order td,
            .dark .printable-orders-report th, .dark .printable-orders-report td {
              border-color: #d1d5db;
              color: #1f2937;
            }
            .dark .printable-purchase-order th, .dark .printable-orders-report th {
              background-color: #1e40af;
              color: white;
            }
          }
          .printable-orders-report {
            display: none;
          }
          @media print {
            .printable-orders-report {
              display: block;
            }
          }
        `}
      </style>
      <div className="py-3 mb-6 text-center text-white rounded-lg shadow-md bg-gradient-to-r from-blue-500 to-blue-800 dark:bg-gradient-to-r dark:from-blue-900 dark:to-slate-800">
        <h1 className="text-2xl font-bold">PURCHASE ORDER</h1>
        <p className="text-sm opacity-90">Manage your purchase orders</p>
      </div>

      <div className="flex items-center justify-end mb-6 gap-x-3">
        <button
          onClick={() => setShowOrders(!showOrders)}
          className="px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          {showOrders ? "Hide Orders" : "View Orders"}
        </button>
        {showOrders && (
          <button
            onClick={handlePrintOrders}
            className="flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            <FiPrinter /> Print All Orders
          </button>
        )}
      </div>

      {showOrders ? (
        <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
              <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                <tr>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">
                    Order ID
                  </th>
                  <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">
                    Supplier
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Total
                  </th>
                  <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
                {orders.length === 0 ? (
                  <tr>
                    <td
                      colSpan={4}
                      className="px-6 py-10 text-center text-gray-500 dark:text-gray-400"
                    >
                      No purchase orders found.
                    </td>
                  </tr>
                ) : (
                  orders.map((order, index) => (
                    <React.Fragment key={order.id}>
                      <tr
                        className={`hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors ${
                          expandedRow === index
                            ? "bg-blue-50 dark:bg-slate-700"
                            : ""
                        }`}
                      >
                        <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">
                          <button
                            onClick={() => toggleRow(index)}
                            className="hover:underline focus:outline-none"
                          >
                            ORD{order.id}
                          </button>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="font-semibold text-gray-900 dark:text-gray-100">
                            {order.supplier?.supplier_name ||
                              order.contact_name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {order.phone}
                          </div>
                        </td>
                        <td className="px-4 py-3 font-semibold text-right text-gray-800 dark:text-white whitespace-nowrap">
                          {formatCurrency(order.total)}
                        </td>
                        <td className="px-4 py-3 text-right whitespace-nowrap">
                          <div className="flex items-center justify-end gap-x-3">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewPrint(order);
                              }}
                              title="View/Reprint"
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none"
                            >
                              <FiPrinter size={16} />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(order);
                              }}
                              title="Edit Order"
                              className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 focus:outline-none"
                            >
                              <FiEdit size={16} />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(order.id);
                              }}
                              title="Delete Order"
                              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                            >
                              <FiTrash2 size={16} />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleRow(index);
                              }}
                              title={
                                expandedRow === index
                                  ? "Collapse Details"
                                  : "Expand Details"
                              }
                              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white focus:outline-none"
                            >
                              {expandedRow === index ? (
                                <FiChevronUp size={18} />
                              ) : (
                                <FiChevronDown size={18} />
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                      {expandedRow === index && (
                        <tr className="bg-gray-50 dark:bg-slate-900/30">
                          <td colSpan={4} className="px-4 py-4 md:px-6 md:py-4">
                            <div className="grid grid-cols-1 gap-6">
                              <div className="p-3 border border-gray-200 rounded-md dark:border-slate-700">
                                <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-600 uppercase dark:text-gray-400">
                                  Order Items ({order.orderItems?.length || 0})
                                </h4>
                                {Array.isArray(order.orderItems) &&
                                order.orderItems.length > 0 ? (
                                  <div className="overflow-x-auto max-h-60">
                                    <table className="min-w-full text-xs divide-y divide-gray-200 dark:divide-slate-600">
                                      <thead className="sticky top-0 text-gray-700 bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                                        <tr>
                                          <th className="px-2 py-1 font-medium text-left">
                                            Description
                                          </th>
                                          <th className="px-2 py-1 font-medium text-center">
                                            Qty
                                          </th>
                                          <th className="px-2 py-1 font-medium text-right">
                                            Unit Price
                                          </th>
                                          <th className="px-2 py-1 font-medium text-right">
                                            Total
                                          </th>
                                        </tr>
                                      </thead>
                                      <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-700">
                                        {order.orderItems.map((item) => (
                                          <tr key={item.id}>
                                            <td className="px-2 py-1 font-medium text-gray-900 dark:text-white">
                                              {item.description || "N/A"}
                                            </td>
                                            <td className="px-2 py-1 text-center text-gray-600 dark:text-gray-300">
                                              {item.qty}
                                            </td>
                                            <td className="px-2 py-1 text-right text-gray-600 dark:text-gray-300">
                                              {formatCurrency(item.unit_price)}
                                            </td>
                                            <td className="px-2 py-1 font-semibold text-right text-gray-900 dark:text-white">
                                              {formatCurrency(item.total)}
                                            </td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                ) : (
                                  <p className="text-sm text-center text-gray-500 dark:text-gray-400">
                                    No item details available.
                                  </p>
                                )}
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <>
          <div className="space-y-6 supplier-info">
            <h3 className="text-base font-medium text-gray-900 dark:text-white">
              Supplier Information
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
                  Supplier
                </label>
                <select
                  name="supplier"
                  value={selectedSupplierId}
                  onChange={handleSupplierChange}
                  className="w-full p-2 bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Supplier</option>
                  {suppliers.map((sup) => (
                    <option key={sup.id} value={sup.id}>
                      {sup.supplier_name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
                  Phone
                </label>
                <input
                  type="text"
                  name="phone"
                  value={supplier.phone}
                  readOnly
                  className="w-full p-2 bg-gray-100 border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white"
                  placeholder="Phone Number"
                />
              </div>
              <div>
                <label className="block mb-1 text-sm font-medium text-gray-600 dark:text-gray-400">
                  Address
                </label>
                <input
                  type="text"
                  name="address"
                  value={supplier.address}
                  readOnly
                  className="w-full p-2 bg-gray-100 border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white"
                  placeholder="Supplier Address"
                />
              </div>
            </div>
          </div>

          <div className="mt-6 space-y-6 items-table">
            <h3 className="text-base font-medium text-gray-900 dark:text-white">
              Order Items
            </h3>
            <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-slate-800 dark:border-slate-700">
              <h4 className="pb-2 mb-4 text-lg font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-600">
                Add New Item
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-12">
                <div className="md:col-span-4">
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Product <span className="text-red-500">*</span>
                  </label>
                  <Select
                    ref={newItemProductSelectRef}
                    options={filteredProductOptions}
                    value={
                      newItem.productId
                        ? filteredProductOptions.find(
                            (option) =>
                              option.value === newItem.productId &&
                              option.variantId === newItem.variantId
                          ) || null
                        : null
                    }
                    placeholder="Search or select product with batch info..."
                    isClearable
                    isSearchable
                    onChange={handleNewItemProductSelect}
                    onInputChange={(value) => setProductSearchTerm(value)}
                    onKeyDown={handleProductSelectKeyDown}
                    styles={customSelectStyles}
                    components={{ Option: CustomOption }}
                    isDarkMode={document.documentElement.classList.contains(
                      "dark"
                    )}
                    menuPortalTarget={document.body}
                    menuShouldScrollIntoView={true}
                  />
                  {errors.newItemDescription && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemDescription}
                    </p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Quantity <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="1"
                    step="1"
                    ref={newItemQtyRef}
                    value={newItem.qty}
                    onChange={(e) => handleNewItemInputChange(e, "qty")}
                    onKeyDown={(e) => handleKeyDown(e, newItemQtyRef)}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.newItemQty ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.newItemQty && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemQty}
                    </p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Unit Price (LKR) <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    ref={newItemUnitPriceRef}
                    value={newItem.unitPrice}
                    onChange={(e) => handleNewItemInputChange(e, "unitPrice")}
                    onKeyDown={(e) => handleKeyDown(e, newItemUnitPriceRef)}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.newItemUnitPrice
                        ? "border-red-500"
                        : "border-gray-300"
                    }`}
                  />
                  {errors.newItemUnitPrice && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemUnitPrice}
                    </p>
                  )}
                </div>
                <div className="md:col-span-2 flex items-end">
                  <button
                    type="button"
                    onClick={handleAddNewItem}
                    className="w-full h-[42px] px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center dark:focus:ring-offset-gray-800"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Add
                  </button>
                </div>
              </div>
            </div>

            <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
              <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
                <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
                  <tr>
                    <th className="px-4 py-3 font-semibold text-left">
                      Product & Batch Details
                    </th>
                    <th className="px-4 py-3 font-semibold text-center">
                      Quantity
                    </th>
                    <th className="px-4 py-3 font-semibold text-right">
                      Unit Price (LKR)
                    </th>
                    <th className="px-4 py-3 font-semibold text-right">
                      Total (LKR)
                    </th>
                    <th className="px-4 py-3 font-semibold text-right"></th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
                  {items.map((item, index) => (
                    <tr
                      key={index}
                      className="transition-colors hover:bg-gray-50 dark:hover:bg-slate-700/50"
                    >
                      <td className="px-4 py-3">
                        <div>
                          <input
                            type="text"
                            name="description"
                            value={item.description}
                            onChange={(e) => handleItemChange(index, e)}
                            className="w-full p-2 bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Product Description"
                          />
                          {item.batchNumber && (
                            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                              Batch: {item.batchNumber}
                              {item.expiryDate &&
                                ` | Exp: ${item.expiryDate.split("T")[0]}`}
                              {item.barcode &&
                                item.barcode !== "N/A" &&
                                ` | Barcode: ${item.barcode}`}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <input
                          type="number"
                          name="qty"
                          value={item.qty}
                          onChange={(e) => handleItemChange(index, e)}
                          className="w-full p-2 text-center bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          min="1"
                        />
                      </td>
                      <td className="px-4 py-3">
                        <input
                          type="number"
                          name="unitPrice"
                          value={item.unitPrice}
                          onChange={(e) => handleItemChange(index, e)}
                          className="w-full p-2 text-right bg-white border border-gray-300 rounded-md dark:bg-gray-900 dark:border-gray-600 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                          step="0.01"
                        />
                      </td>
                      <td className="px-4 py-3 font-semibold text-right text-gray-900 dark:text-white">
                        {formatCurrency(item.total)}
                      </td>
                      <td className="px-4 py-3 text-right">
                        <button
                          onClick={() => removeItem(index)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                        >
                          <FiTrash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-6 text-right total">
            <h3 className="text-base font-semibold text-gray-900 dark:text-white">
              Total: {formatCurrency(calculateTotal())}
            </h3>
          </div>

          <div className="mt-6 text-center">
            <button
              onClick={handleSubmit}
              className="px-8 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
            >
              {editingOrder ? "Update Order" : "Submit Order"}
            </button>
          </div>

          {error && (
            <div className="mt-4 text-center text-red-500">{error}</div>
          )}
          {success && (
            <div className="mt-4 text-center text-green-500">{success}</div>
          )}
        </>
      )}

      {printOrderData && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm animate-fade-in">
          <div className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden bg-white rounded-lg shadow-xl dark:bg-gray-800 flex flex-col">
            <div className="flex items-center justify-between flex-shrink-0 p-4 border-b dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Purchase Order Preview (#{printOrderData.order.no})
              </h3>
              <button
                onClick={closePrintModal}
                className="p-1 text-gray-500 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 dark:text-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                aria-label="Close preview"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="flex-grow overflow-y-auto">
              <PrintablePurchaseOrder orderData={printOrderData} />
            </div>
            <div className="flex justify-end flex-shrink-0 p-4 border-t dark:border-gray-700">
              <button
                onClick={() => window.print()}
                className="flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              >
                <FiPrinter /> Print
              </button>
            </div>
          </div>
        </div>
      )}

      {printAllOrders && (
        <PrintableOrdersReport
          orders={orders}
          formatCurrency={formatCurrency}
        />
      )}
    </div>
  );
};

export default PurchaseOrder;
