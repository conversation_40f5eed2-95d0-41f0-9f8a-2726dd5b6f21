import React, { useState, useEffect } from "react";
import axios from "axios";
import { motion, AnimatePresence } from "framer-motion";
import { FaPrint, FaChevronDown, FaChevronUp } from "react-icons/fa";
import { useReactToPrint } from "react-to-print";

const Ledger = () => {
  const [personType, setPersonType] = useState("");
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [selectedPersonId, setSelectedPersonId] = useState("");
  const [outstandingInvoices, setOutstandingInvoices] = useState([]);
  const [customerSummaries, setCustomerSummaries] = useState([]);
  const [expandedCustomers, setExpandedCustomers] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const printRef = React.useRef();

  // Fetch customers and suppliers on mount
  useEffect(() => {
    fetchCustomers();
    fetchSuppliers();
  }, []);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await axios.get("http://localhost:8000/api/customers");
      setCustomers(Array.isArray(response.data) ? response.data : response.data.data || []);
    } catch (err) {
      setError("Failed to fetch customers.");
      console.error("Error fetching customers:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const response = await axios.get("http://localhost:8000/api/suppliers");
      setSuppliers(Array.isArray(response.data) ? response.data : response.data.data || []);
    } catch (err) {
      setError("Failed to fetch suppliers.");
      console.error("Error fetching suppliers:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchOutstandingInvoices = async (personId, personType) => {
    try {
      setLoading(true);
      const endpoint = personType === "customer" ? "outstanding" : "supplier-outstanding";
      const response = await axios.get(`http://localhost:8000/api/${endpoint}`, {
        params: { [`${personType}_id`]: personId },
      });
      const invoices = Array.isArray(response.data) ? response.data : [];
      setOutstandingInvoices(invoices);

      // Calculate summaries
      const summaries = calculateSummaries(invoices, personType);
      setCustomerSummaries(summaries);
    } catch (err) {
      setError(`Failed to fetch ${personType} outstanding invoices.`);
      console.error(`Error fetching ${personType} invoices:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Calculate summaries for total pending, paid, and balance
  const calculateSummaries = (invoices, personType) => {
    const summaryMap = {};

    invoices.forEach((invoice) => {
      const personId = personType === "customer" ? invoice.customer_id : invoice.supplier_id;
      const personName = personType === "customer" ? invoice.customer_name : invoice.supplier_name;

      if (!summaryMap[personId]) {
        summaryMap[personId] = {
          person_id: personId,
          person_name: personName,
          total_amount: 0,
          paid_amount: 0,
          outstanding_amount: 0,
          invoices: [],
          status: "Paid",
        };
      }

      summaryMap[personId].total_amount += invoice.total_amount || 0;
      summaryMap[personId].paid_amount += invoice.paid_amount || 0;
      summaryMap[personId].outstanding_amount += invoice.final_outstanding_amount || 0;
      summaryMap[personId].invoices.push(invoice);

      if (invoice.status !== "Paid") {
        summaryMap[personId].status = invoice.status === "Partial" ? "Partial" : "Unpaid";
      }
    });

    return Object.values(summaryMap);
  };

  // Handle person type change
  const handlePersonTypeChange = (e) => {
    setPersonType(e.target.value);
    setSelectedPersonId("");
    setOutstandingInvoices([]);
    setCustomerSummaries([]);
    setExpandedCustomers({});
  };

  // Handle person selection
  const handlePersonChange = (e) => {
    const personId = e.target.value;
    setSelectedPersonId(personId);
    if (personId && personType) {
      fetchOutstandingInvoices(personId, personType);
    } else {
      setOutstandingInvoices([]);
      setCustomerSummaries([]);
    }
  };

  // Toggle customer/supplier expansion
  const toggleExpansion = (personId) => {
    setExpandedCustomers((prev) => ({
      ...prev,
      [personId]: !prev[personId],
    }));
  };

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "LKR",
    }).format(value);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge class
  const getStatusClass = (status) => {
    if (status === "Paid") {
      return "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100";
    } else if (status === "Partial") {
      return "bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100";
    } else {
      return "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100";
    }
  };

  // Handle print
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    pageStyle: `
      @page { size: A4 landscape; margin: 10mm; }
      @media print {
        body { -webkit-print-color-adjust: exact; }
        .no-print { display: none !important; }
        .print-table { width: 100%; border-collapse: collapse; }
        .print-table th, .print-table td { border: 1px solid #ddd; padding: 8px; }
        .print-table th { background-color: #f2f2f2; }
        .print-table tr:nth-child(even) { background-color: #f9f9f9; }
      }
    `,
  });

  return (
    <div className="min-h-screen p-4 text-gray-900 bg-white dark:bg-gray-900 dark:text-white md:p-6">
      <motion.h2
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-6 text-2xl font-semibold"
      >
        Ledger
      </motion.h2>

      {/* Status Messages */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="p-4 mb-4 text-red-700 bg-red-100 rounded-lg dark:bg-red-900 dark:text-red-100"
          >
            {error}
          </motion.div>
        )}
        {successMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="p-4 mb-4 text-green-700 bg-green-100 rounded-lg dark:bg-green-900 dark:text-green-100"
          >
            {successMessage}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Form */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="p-6 mb-6 bg-white rounded-lg shadow-md dark:bg-gray-800"
      >
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <label htmlFor="personType" className="block mb-1 text-sm font-medium">
              Person Type
            </label>
            <select
              id="personType"
              value={personType}
              onChange={handlePersonTypeChange}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Person Type</option>
              <option value="customer">Customer</option>
              <option value="supplier">Supplier</option>
            </select>
          </div>
          <div>
            <label htmlFor="personId" className="block mb-1 text-sm font-medium">
              {personType === "customer" ? "Customer" : personType === "supplier" ? "Supplier" : "Select Person"}
            </label>
            <select
              id="personId"
              value={selectedPersonId}
              onChange={handlePersonChange}
              className="w-full p-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!personType}
            >
              <option value="">Select {personType === "customer" ? "Customer" : "Supplier"}</option>
              {(personType === "customer" ? customers : suppliers).map((person) => (
                <option key={person.id} value={person.id}>
                  {personType === "customer" ? person.customer_name : person.supplier_name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </motion.div>

      {/* Outstanding Details */}
      {customerSummaries.length > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-lg shadow-md dark:bg-gray-800"
          ref={printRef}
        >
          <table className="w-full table-auto print-table">
            <thead className="bg-gray-100 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left no-print"></th>
                <th className="px-4 py-3 text-left">{personType === "customer" ? "Customer" : "Supplier"}</th>
                <th className="px-4 py-3 text-left">Total Amount</th>
                <th className="px-4 py-3 text-left">Paid Amount</th>
                <th className="px-4 py-3 text-left">Pending Amount</th>
                <th className="px-4 py-3 text-left">Status</th>
                <th className="px-4 py-3 text-left no-print">Actions</th>
              </tr>
            </thead>
            <tbody>
              {customerSummaries.map((summary) => (
                <React.Fragment key={summary.person_id}>
                  <tr className="border-t border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 no-print">
                      <button
                        onClick={() => toggleExpansion(summary.person_id)}
                        className="p-1 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                      >
                        {expandedCustomers[summary.person_id] ? <FaChevronUp /> : <FaChevronDown />}
                      </button>
                    </td>
                    <td className="px-4 py-3 font-medium">
                      {summary.person_name}
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        ID: {summary.person_id}
                      </div>
                    </td>
                    <td className="px-4 py-3">{formatCurrency(summary.total_amount)}</td>
                    <td className="px-4 py-3">{formatCurrency(summary.paid_amount)}</td>
                    <td className="px-4 py-3">{formatCurrency(summary.outstanding_amount)}</td>
                    <td className="px-4 py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(summary.status)}`}>
                        {summary.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 no-print">
                      <button
                        onClick={handlePrint}
                        className="px-3 py-1 text-sm text-white bg-gray-600 rounded-md hover:bg-gray-700"
                      >
                        <FaPrint className="inline mr-2" />
                        Print
                      </button>
                    </td>
                  </tr>
                  {expandedCustomers[summary.person_id] && (
                    <tr className="bg-gray-50 dark:bg-gray-700">
                      <td colSpan="7" className="px-4 py-3">
                        <div className="pl-8">
                          <h4 className="mb-2 font-medium">Invoice Details</h4>
                          <table className="w-full mt-2 border border-gray-200 dark:border-gray-600">
                            <thead className="bg-gray-200 dark:bg-gray-600">
                              <tr>
                                <th className="px-3 py-2 text-left">Invoice #</th>
                                <th className="px-3 py-2 text-left">Date</th>
                                <th className="px-3 py-2 text-left">Total</th>
                                <th className="px-3 py-2 text-left">Paid</th>
                                <th className="px-3 py-2 text-left">Pending</th>
                                <th className="px-3 py-2 text-left">Status</th>
                              </tr>
                            </thead>
                            <tbody>
                              {summary.invoices.map((invoice) => (
                                <tr key={invoice.id} className="border-t border-gray-200 dark:border-gray-600">
                                  <td className="px-3 py-2">#{invoice.id}</td>
                                  <td className="px-3 py-2">{formatDate(invoice.date)}</td>
                                  <td className="px-3 py-2">{formatCurrency(invoice.total_amount)}</td>
                                  <td className="px-3 py-2">{formatCurrency(invoice.paid_amount)}</td>
                                  <td className="px-3 py-2">{formatCurrency(invoice.final_outstanding_amount)}</td>
                                  <td className="px-3 py-2">
                                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(invoice.status)}`}>
                                      {invoice.status}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </motion.div>
      )}

      {/* Loading Indicator */}
      {loading && (
        <div className="flex items-center justify-center p-8">
          <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};

export default Ledger;