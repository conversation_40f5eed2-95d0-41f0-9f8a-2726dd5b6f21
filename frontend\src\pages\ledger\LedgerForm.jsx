import React, { useState, useEffect, useRef } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import { AccountGroupModal } from "./AccountGroupModal";

export const LedgerForm = ({ onSubmit, onCancel, editId }) => {
  const [formData, setFormData] = useState({
    staff_id: "",
    name: "",
    address: "",
    mobile_no: "",
    whatsapp_no: "",
    telephone_no: "",
    nic: "",
    opening_balance: "",
    account_group: "",
    email: "",
    position: "",
    join_date: "",
    date: new Date().toISOString().split("T")[0],
    profile_picture: null,
  });

  const [errors, setErrors] = useState({
    staff_id: false,
    name: false,
    account_group: false,
  });

  const [isSubGroupModalOpen, setIsSubGroupModalOpen] = useState(false);
  const [subGroups, setSubGroups] = useState([]);

  const mainAccountGroups = [
    "Stock in hand",
    "Purchase Account",
    "Direct Expenses",
    "Sales Accounts",
    "Indirect Expenses",
    "Indirect Income",
    "Loan Liabilities",
    "Bank OD",
    "Current Liabilities",
    "Sundry Creditors",
    "Capital Account",
    "Bank Accounts",
    "Cash in Hand",
    "Current Asset",
    "Sundry Debtors",
    "Fixed Asset",
  ];

  const formRef = useRef(null);
  const submitButtonRef = useRef(null);
  const cancelButtonRef = useRef(null);

  const API_URL = "http://localhost:8000/api/staff-ledger";
  const SUB_GROUPS_API_URL = "http://localhost:8000/api/account-sub-groups";

  // Fetch sub groups
  const fetchSubGroups = async () => {
    try {
      const response = await axios.get(SUB_GROUPS_API_URL);
      setSubGroups(response.data.data);
    } catch (error) {
      console.error("Error fetching sub groups:", error);
    }
  };

  // Create combined account groups (main groups + sub groups)
  const getCombinedAccountGroups = () => {
    const result = [...mainAccountGroups];

    // Add sub groups directly without main group prefix
    subGroups.forEach(subGroup => {
      result.push(subGroup.sub_group_name);
    });

    return result;
  };

  // Fetch sub groups on component mount
  useEffect(() => {
    fetchSubGroups();
  }, []);

  // Fetch ledger data for editing
  useEffect(() => {
    if (editId) {
      axios
        .get(`${API_URL}/${editId}`)
        .then((response) => {
          const data = response.data.data;
          setFormData({
            staff_id: data.staff_id || "",
            name: data.name || "",
            address: data.address || "",
            mobile_no: data.mobile_no || "",
            whatsapp_no: data.whatsapp_no || "",
            telephone_no: data.telephone_no || "",
            nic: data.nic || "",
            opening_balance: data.opening_balance || "",
            account_group: data.account_group || "",
            email: data.email || "",
            position: data.position || "",
            join_date: data.join_date || "",
            date: data.date || new Date().toISOString().split("T")[0],
            profile_picture: null,
          });
        })
        .catch((error) => {
          toast.error("Failed to fetch ledger data");
          console.error(error);
        });
    }
  }, [editId]);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate required fields
    const newErrors = {
      staff_id: false, // Staff ID is optional
      name: !formData.name,
      account_group: !formData.account_group,
    };

    setErrors(newErrors);

    if (!formData.name || !formData.account_group) {
      toast.error("Please fill all required fields");
      return;
    }

    onSubmit(formData);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();

      const form = formRef.current;
      const inputs = Array.from(form.querySelectorAll("input, select")).filter(
        (el) => !el.disabled
      );
      const currentIndex = inputs.indexOf(e.target);

      if (currentIndex < inputs.length - 1) {
        inputs[currentIndex + 1].focus();
      } else {
        submitButtonRef.current.focus();
      }
    }
  };

  const handleButtonKeyDown = (e) => {
    if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
      if (e.target === submitButtonRef.current) {
        cancelButtonRef.current.focus();
      } else if (e.target === cancelButtonRef.current) {
        submitButtonRef.current.focus();
      }
    } else if (e.key === "Enter") {
      if (e.target === submitButtonRef.current) {
        handleSubmit(e);
      } else if (e.target === cancelButtonRef.current) {
        onCancel();
      }
    }
  };

  return (
    <div className="fixed inset-0 z-50 backdrop-blur-sm bg-black/30">
      <form
        ref={formRef}
        onSubmit={handleSubmit}
        className="w-full h-screen overflow-y-auto bg-slate-100 dark:bg-slate-800 min-w-max"
      >
        <div className="max-w-4xl px-6 py-8 mx-auto">
          {/* Header Section */}
          <div className="flex items-center justify-between pb-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100">
                {editId ? "Edit Ledger" : "Add New Ledger"}
              </h2>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {editId ? "Update the ledger details" : "Add a new ledger to your accounting system"}
              </p>
            </div>
            <button
              type="button"
              onClick={onCancel}
              className="p-2 text-gray-500 transition-colors rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              aria-label="Close"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Main Form Content */}
          <div className="mt-8 space-y-8">
            {/* Basic Information Section */}
            <div className="max-w-full p-8 mx-auto bg-white border border-gray-200 shadow-xl dark:bg-gray-900 rounded-2xl dark:border-gray-700">
              <h3 className="mb-8 text-2xl font-semibold text-center text-gray-800 dark:text-white">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {/* Staff ID */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Staff ID
                  </label>
                  <input
                    type="text"
                    name="staff_id"
                    value={formData.staff_id}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className={`block w-full px-3 py-2 border ${
                      errors.staff_id ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    } rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                    placeholder="auto-generation(STF-001)"
                    disabled
                  />
                  {errors.staff_id && <p className="mt-1 text-xs text-red-500">Staff ID must be unique</p>}
                </div>

                {/* Name (required) */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className={`block w-full px-3 py-2 border ${
                      errors.name ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    } rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                    placeholder="Enter ledger name"
                    required
                  />
                  {errors.name && <p className="mt-1 text-xs text-red-500">Name is required</p>}
                </div>

                {/* Account Group (required) */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Account Group <span className="text-red-500">*</span>
                    </label>
                    <button
                      type="button"
                      onClick={() => setIsSubGroupModalOpen(true)}
                      className="px-3 py-1 text-xs font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Sub Group
                    </button>
                  </div>
                  <select
                    name="account_group"
                    value={formData.account_group}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className={`block w-full px-3 py-2 border ${
                      errors.account_group ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    } rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                    required
                  >
                    <option value="">Select Account Group</option>
                    {getCombinedAccountGroups().map((group, index) => (
                      <option key={index} value={group}>
                        {group}
                      </option>
                    ))}
                  </select>
                  {errors.account_group && (
                    <p className="mt-1 text-xs text-red-500">Account Group is required</p>
                  )}
                </div>

                {/* Address */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Address
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter address"
                  />
                </div>

                {/* Opening Balance */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Opening Balance
                  </label>
                  <div className="relative rounded-md shadow-sm">
                    <input
                      type="number"
                      name="opening_balance"
                      value={formData.opening_balance}
                      onChange={handleChange}
                      onKeyDown={handleKeyDown}
                      className="block w-full py-2 pr-12 border border-gray-300 rounded-md pl-7 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="LKR 0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="max-w-full p-8 mx-auto bg-white border border-gray-200 shadow-xl dark:bg-gray-900 rounded-2xl dark:border-gray-700">
              <h3 className="mb-8 text-2xl font-semibold text-center text-gray-800 dark:text-white">
                Contact Information
              </h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {/* Mobile No */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Mobile No
                  </label>
                  <input
                    type="text"
                    name="mobile_no"
                    value={formData.mobile_no}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter mobile number"
                  />
                </div>

                {/* WhatsApp No */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    WhatsApp No
                  </label>
                  <input
                    type="text"
                    name="whatsapp_no"
                    value={formData.whatsapp_no}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter WhatsApp number"
                  />
                </div>

                {/* Telephone No */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Telephone No
                  </label>
                  <input
                    type="text"
                    name="telephone_no"
                    value={formData.telephone_no}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter telephone number"
                  />
                </div>

                {/* Email */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter email address"
                  />
                </div>
              </div>
            </div>

            {/* Additional Information Section */}
            <div className="max-w-full p-8 mx-auto bg-white border border-gray-200 shadow-xl dark:bg-gray-900 rounded-2xl dark:border-gray-700">
              <h3 className="mb-8 text-2xl font-semibold text-center text-gray-800 dark:text-white">
                Additional Information
              </h3>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {/* NIC */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    NIC
                  </label>
                  <input
                    type="text"
                    name="nic"
                    value={formData.nic}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter NIC number"
                  />
                </div>

                {/* Position */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Position
                  </label>
                  <input
                    type="text"
                    name="position"
                    value={formData.position}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter position"
                  />
                </div>

                {/* Join Date */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Join Date
                  </label>
                  <input
                    type="date"
                    name="join_date"
                    value={formData.join_date}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Date */}
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Date
                  </label>
                  <input
                    type="date"
                    name="date"
                    value={formData.date}
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Profile Picture */}
                <div className="space-y-1 sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Profile Picture
                  </label>
                  <div className="flex items-center">
                    <input
                      type="file"
                      name="profile_picture"
                      onChange={handleChange}
                      onKeyDown={handleKeyDown}
                      className="block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 dark:file:bg-indigo-900/50 file:text-indigo-700 dark:file:text-indigo-300 hover:file:bg-indigo-100 dark:hover:file:bg-indigo-800/50"
                      accept="image/*"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                ref={cancelButtonRef}
                onClick={onCancel}
                onKeyDown={handleButtonKeyDown}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm dark:border-gray-600 dark:text-gray-300 dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                ref={submitButtonRef}
                onKeyDown={handleButtonKeyDown}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {editId ? "Update Ledger" : "Create Ledger"}
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* Account Group Modal */}
      <AccountGroupModal
        isOpen={isSubGroupModalOpen}
        onClose={() => setIsSubGroupModalOpen(false)}
        onSubGroupAdded={fetchSubGroups}
      />

      <ToastContainer position="bottom-right" autoClose={3000} />
    </div>
  );
};