import React, { useState, useEffect } from "react";
import {
  Plus,
  Upload,
  Download,
  Trash2,
  Loader2,
  Search,
  Eye,
} from "lucide-react";
import { FiEdit, FiTrash2 } from "react-icons/fi";
import axios from "axios";
import * as XLSX from "xlsx";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import ItemForm from "../../components/Item Form/ItemForm";

import ProductDetailsModal from "./ProductDetailsModal";
import ConfirmationModal from "../../pages/ConfirmationModal";
import { useAuth } from "../../context/NewAuthContext";
import ProgressOverlay from "../../components/ProgressOverlay";

const Pagination = ({
  currentPage,
  totalItems,
  itemsPerPage,
  paginate,
  maxVisiblePages = 5,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  if (totalPages <= 1) return null;

  const getPageNumbers = () => {
    const halfVisible = Math.floor(maxVisiblePages / 2);
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    const pages = [];

    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      if (i >= 1 && i <= totalPages) {
        pages.push(i);
      }
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push("...");
      }
      pages.push(totalPages);
    }

    return pages;
  };

  return (
    <div className="flex flex-wrap items-center justify-center gap-1 p-2">
      <button
        onClick={() => paginate(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className={`px-3 py-1 rounded-md ${
          currentPage === 1
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 hover:bg-gray-200"
        }`}
      >
        Prev
      </button>

      {getPageNumbers().map((page, index) => (
        <button
          key={index}
          onClick={() => (typeof page === "number" ? paginate(page) : null)}
          className={`px-3 py-1 rounded-md min-w-[2.5rem] ${
            page === currentPage
              ? "bg-blue-600 text-white"
              : typeof page === "number"
                ? "text-gray-700 hover:bg-gray-200"
                : "text-gray-500 cursor-default"
          }`}
          disabled={page === "..." || page === currentPage}
        >
          {page}
        </button>
      ))}

      <button
        onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className={`px-3 py-1 rounded-md ${
          currentPage === totalPages
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 hover:bg-gray-200"
        }`}
      >
        Next
      </button>
    </div>
  );
};

const Items = () => {
  const { currentUser } = useAuth();
  const [showForm, setShowForm] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteMode, setDeleteMode] = useState(null); // 'selected' or 'all'
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [selectedFile, setSelectedFile] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]); // New state for selected items
  const [isImporting, setIsImporting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [isDeletingSelected, setIsDeletingSelected] = useState(false);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isBatchMode, setIsBatchMode] = React.useState(false);
  const [pendingBatchData, setPendingBatchData] = React.useState(null);
  // Remove barcodeSearch state

  // Reset batch mode on component mount to ensure clean state
  React.useEffect(() => {
    setIsBatchMode(false);
  }, []);

  // Handle showing form when batch data is ready
  React.useEffect(() => {
    console.log("Batch form useEffect - pendingBatchData:", pendingBatchData);
    console.log("Batch form useEffect - selectedItem:", selectedItem);
    console.log("Batch form useEffect - isBatchMode:", isBatchMode);
    
    if (pendingBatchData && selectedItem && isBatchMode) {
      console.log("=== SHOWING BATCH FORM ===");
      setShowForm(true);
      setPendingBatchData(null);
    }
  }, [pendingBatchData, selectedItem, isBatchMode]);

  const api = axios.create({
    baseURL: "http://127.0.0.1:8000/api",
    headers: {
      Authorization: `Bearer ${currentUser?.token}`,
      "Content-Type": "application/json",
    },
  });

  useEffect(() => {
    fetchItems();
  }, [currentPage]);

  const fetchItems = async () => {
    setLoading(true);
    try {
      const response = await api.get("/products");
      // console.log("API /products response:", response);
      setItems(response.data.data);
      setFilteredItems(response.data.data);
    } catch (error) {
      console.error("Error fetching items:", error);
      if (error.response?.status === 401) {
        toast.error("Session expired. Please login again.");
      } else {
        toast.error("Error fetching items: " + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (Array.isArray(items)) {
      setFilteredItems(
        items.filter((item) => {
          const search = searchQuery.trim().toLowerCase();
          if (!search) return true;
          // Product name, item code, description, category
          const matchesText =
            item.product_name.toLowerCase().includes(search) ||
            (item.item_code && item.item_code.toLowerCase().includes(search)) ||
            (item.description && item.description.toLowerCase().includes(search)) ||
            (item.category_name && item.category_name.toLowerCase().includes(search)) ||
            (item.category && item.category.toLowerCase().includes(search));
          // Barcode (main or any variant)
          const matchesBarcode =
            (item.barcode && item.barcode.toLowerCase().includes(search)) ||
            (Array.isArray(item.variants) && item.variants.some(v => v.barcode && v.barcode.toLowerCase().includes(search)));
          // Batch number (main or any variant)
          const matchesBatch =
            (item.batch_number && item.batch_number.toLowerCase().includes(search)) ||
            (Array.isArray(item.variants) && item.variants.some(v => v.batch_number && v.batch_number.toLowerCase().includes(search)));
          return matchesText || matchesBarcode || matchesBatch;
        })
      );
    }
  }, [searchQuery, items]);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      toast.success("File selected: " + file.name);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error("Please select a file first.");
      return;
    }

    const formData = new FormData();
    formData.append("file", selectedFile);

    try {
      setIsImporting(true);
      setUploadProgress(0);
      console.log("Starting import with file:", selectedFile.name);
      
      const response = await api.post("/products/import", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setUploadProgress(percentCompleted);
          }
        },
      });
      
      console.log("Import response:", response.data);
      toast.success(response.data.message + ` (${response.data.count || 0} products imported)`);
      setSelectedFile(null);
      
      // Refresh the items list
      await fetchItems();
      
      console.log("Import completed successfully");
    } catch (error) {
      console.error("Error importing items:", error);
      console.error("Error response:", error.response?.data);
      toast.error(
        "Error importing items: " +
          (error.response?.data?.message || error.message)
      );
    } finally {
      setIsImporting(false);
      setUploadProgress(0);
    }
  };
  const handleExport = async (items, setIsExporting, toast) => {
    if (!Array.isArray(items) || items.length === 0) {
      toast.error("No items to export");
      return;
    }
    try {
      setIsExporting(true);

      // Create export data including batch-wise details
      const exportData = [];

      items.forEach((item) => {
        // Check if item has variants (batch-wise details)
        if (item.variants && item.variants.length > 0) {
          // Export each variant as a separate row
          item.variants.forEach((variant) => {
            exportData.push({
              product_name: item.product_name ? String(item.product_name) : "",
              item_code: item.item_code ? String(item.item_code) : "",
              batch_number: variant.batch_number
                ? String(variant.batch_number)
                : "",
              expiry_date: variant.expiry_date
                ? new Date(variant.expiry_date)
                : null,
              buying_cost: variant.buying_cost
                ? Number(variant.buying_cost)
                : 0,
              sales_price: variant.sales_price
                ? Number(variant.sales_price)
                : 0,
              minimum_price: variant.minimum_price
                ? Number(variant.minimum_price)
                : 0,
              wholesale_price: variant.wholesale_price
                ? Number(variant.wholesale_price)
                : 0,
              barcode: variant.barcode ? String(variant.barcode) : "",
              mrp: variant.mrp ? Number(variant.mrp) : 0,
              minimum_stock_quantity: variant.minimum_stock_quantity
                ? Number(variant.minimum_stock_quantity)
                : 0,
              opening_stock_quantity: variant.opening_stock_quantity
                ? Number(variant.opening_stock_quantity)
                : 0,
              opening_stock_value: variant.opening_stock_value
                ? Number(variant.opening_stock_value)
                : 0,
              category: item.category ? String(item.category) : "",
              supplier: item.supplier ? String(item.supplier) : "",
              unit_type: item.unit_type ? String(item.unit_type) : "",
              store_location: variant.store_location
                ? String(variant.store_location)
                : "",
              company: item.company ? String(item.company) : "",
              cabinet: item.cabinet ? String(item.cabinet) : "",
              row: item.row ? String(item.row) : "",
              extra_field_name: item.extra_fields?.extra_field_name || "",
              extra_field_value: item.extra_fields?.extra_field_value || "",
            });
          });
        } else {
          // Export base product without variants
          exportData.push({
            product_name: item.product_name ? String(item.product_name) : "",
            item_code: item.item_code ? String(item.item_code) : "",
            batch_number: item.batch_number ? String(item.batch_number) : "",
            expiry_date: item.expiry_date ? new Date(item.expiry_date) : null,
            buying_cost: item.buying_cost ? Number(item.buying_cost) : 0,
            sales_price: item.sales_price ? Number(item.sales_price) : 0,
            minimum_price: item.minimum_price ? Number(item.minimum_price) : 0,
            wholesale_price: item.wholesale_price
              ? Number(item.wholesale_price)
              : 0,
            barcode: item.barcode ? String(item.barcode) : "",
            mrp: item.mrp ? Number(item.mrp) : 0,
            minimum_stock_quantity: item.minimum_stock_quantity
              ? Number(item.minimum_stock_quantity)
              : 0,
            opening_stock_quantity: item.opening_stock_quantity
              ? Number(item.opening_stock_quantity)
              : 0,
            opening_stock_value: item.opening_stock_value
              ? Number(item.opening_stock_value)
              : 0,
            category: item.category ? String(item.category) : "",
            supplier: item.supplier ? String(item.supplier) : "",
            unit_type: item.unit_type ? String(item.unit_type) : "",
            store_location: item.store_location
              ? String(item.store_location)
              : "",
            company: item.company ? String(item.company) : "",
            cabinet: item.cabinet ? String(item.cabinet) : "",
            row: item.row ? String(item.row) : "",
            extra_field_name: item.extra_fields?.extra_field_name || "",
            extra_field_value: item.extra_fields?.extra_field_value || "",
          });
        }
      });

      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // Set column headers in the correct order
      const headers = [
        "product_name",
        "item_code",
        "batch_number",
        "expiry_date",
        "buying_cost",
        "sales_price",
        "minimum_price",
        "wholesale_price",
        "barcode",
        "mrp",
        "minimum_stock_quantity",
        "opening_stock_quantity",
        "opening_stock_value",
        "category",
        "supplier",
        "unit_type",
        "store_location",
        "company",
        "cabinet",
        "row",
        "extra_field_name",
        "extra_field_value",
      ];

      // Replace the header row with the correct column names
      XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: "A1" });

      // Set cell types explicitly for proper Excel data types
      const range = XLSX.utils.decode_range(worksheet["!ref"]);
      for (let R = range.s.r + 1; R <= range.e.r; ++R) {
        // skip header row
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cell_address = { c: C, r: R };
          const cell_ref = XLSX.utils.encode_cell(cell_address);
          const cell = worksheet[cell_ref];
          if (!cell) continue;

          // Determine column by C index
          switch (C) {
            case 0: // product_name - string
            case 1: // item_code - string
            case 2: // batch_number - string
            case 8: // barcode - string
            case 13: // category - string
            case 14: // supplier - string
            case 15: // unit_type - string
            case 16: // store_location - string
            case 17: // cabinet - string
            case 18: // row - string
            case 19: // extra_field_name - string
            case 20: // extra_field_value - string
              cell.t = "s";
              break;
            case 3: // expiry_date - date
              if (cell.v instanceof Date) {
                cell.t = "d";
                cell.z = XLSX.SSF._table[14]; // date format 'm/d/yy'
              } else if (typeof cell.v === "string") {
                // Try to parse date string
                const dt = new Date(cell.v);
                if (!isNaN(dt)) {
                  cell.v = dt;
                  cell.t = "d";
                  cell.z = XLSX.SSF._table[14];
                }
              }
              break;
            case 4: // buying_cost - number
            case 5: // sales_price - number
            case 6: // minimum_price - number
            case 7: // wholesale_price - number
            case 9: // mrp - number
            case 10: // minimum_stock_quantity - number
            case 11: // opening_stock_quantity - number
            case 12: // opening_stock_value - number
              cell.t = "n";
              break;
            default:
              cell.t = "s";
          }
        }
      }

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Items");
      XLSX.writeFile(workbook, "items_export.xlsx");
    } catch (error) {
      toast.error("Error exporting items: " + error.message);
    } finally {
      setIsExporting(false);
    }
  };
  const handleEditItem = (item) => {
    if (item && item.product_id) {
      setSelectedItem(item);
      setIsBatchMode(false);
      setShowForm(true);
    } else {
      console.error("Selected item does not have a product_id:", item);
      toast.error("Invalid item selected for editing.");
    }
  };

  const handleAddItem = async (newItem) => {
    try {
      // If editing an existing item, skip name check and proceed with update
      if (selectedItem) {
        const response = await api.put(
          `/products/${selectedItem.product_id}`,
          newItem
        );
        toast.success(response.data.message);
        setShowForm(false);
        setSelectedItem(null);
        setIsBatchMode(false);
        fetchItems();
        return;
      }

      // For new items, check if product_name exists
      const checkResponse = await api.get("/products/check-names", {
        params: { names: [newItem.product_name] },
      });
      const existingNames = checkResponse.data.existing || [];

      if (existingNames.includes(newItem.product_name)) {
        console.log("=== PRODUCT EXISTS - ENTERING BATCH MODE ===");
        console.log("newItem.product_name:", newItem.product_name);
        console.log("existingNames:", existingNames);
        
        // Product exists, open form in batch-only mode
        const productResponse = await api.get("/products", {
          params: { product_name: newItem.product_name },
        });

        console.log("productResponse.data.data:", productResponse.data.data);

        // Find exact match to prevent wrong product selection
        const exactMatch = productResponse.data.data.find(
          (p) => p.product_name === newItem.product_name
        );

        console.log("exactMatch found:", exactMatch);

        if (!exactMatch) {
          toast.error(
            `No exact match found for product "${newItem.product_name}". Please check the product name.`
          );
          return;
        }

        // Set the existing product as selectedItem and open form in batch mode
        console.log("=== SETTING BATCH MODE ===");
        console.log("Setting batch mode with exactMatch:", exactMatch);
        console.log("exactMatch.product_name:", exactMatch.product_name);
        console.log("exactMatch.item_code:", exactMatch.item_code);
        setSelectedItem(exactMatch);
        setIsBatchMode(true);
        setPendingBatchData(exactMatch);
        console.log("State updates queued - selectedItem, isBatchMode, pendingBatchData");
        return;
      }

      // If product does not exist, proceed with creation
      const response = await api.post("/products", newItem);

      toast.success(response.data.message);
      setShowForm(false);
      setSelectedItem(null);
      setIsBatchMode(false);
      fetchItems();
    } catch (error) {
      console.error("Error saving item:", error);
      console.error("Response data:", error.response?.data);
      toast.error(
        "Error saving item: " + (error.response?.data?.message || error.message)
      );
    }
  };

  const handleDeleteItem = async (product_id) => {
    try {
      setIsDeletingSelected(true);
      // Create axios instance without timeout for delete operations
      const deleteConfig = {
        timeout: 0, // No timeout
        headers: {
          Authorization: `Bearer ${JSON.parse(localStorage.getItem("user") || sessionStorage.getItem("user") || "{}").token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      };

      await axios.delete(
        `http://127.0.0.1:8000/api/products/${product_id}`,
        deleteConfig
      );
      toast.success("Item deleted successfully");
      fetchItems();
    } catch (error) {
      console.error("Error deleting item:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Unknown error occurred";
      toast.error("Error deleting item: " + errorMessage);
    } finally {
      setShowDeleteModal(false);
      setSelectedItem(null);
      setIsDeletingSelected(false);
    }
  };

  // New function to handle multi-delete
  const handleDeleteSelectedItems = async () => {
    try {
      setIsDeletingSelected(true);
      setIsDeleting(true);

      // Create axios config without timeout for delete operations
      const deleteConfig = {
        timeout: 0, // No timeout
        headers: {
          Authorization: `Bearer ${JSON.parse(localStorage.getItem("user") || sessionStorage.getItem("user") || "{}").token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      };

      const deletePromises = selectedItems.map(async (product_id) => {
        try {
          await axios.delete(
            `http://127.0.0.1:8000/api/products/${product_id}`,
            deleteConfig
          );
          return { success: true, product_id };
        } catch (error) {
          return {
            success: false,
            product_id,
            error: error.response?.data?.message || error.message,
          };
        }
      });

      const results = await Promise.all(deletePromises);
      const failed = results.filter((r) => !r.success);
      const succeeded = results.filter((r) => r.success);

      if (succeeded.length > 0) {
        toast.success(`${succeeded.length} item(s) deleted successfully`);
      }

      if (failed.length > 0) {
        failed.forEach((f) => {
          toast.error(`Failed to delete item ${f.product_id}: ${f.error}`);
        });
      }

      setSelectedItems([]);
      fetchItems();
    } catch (error) {
      console.error("Error deleting selected items:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Unknown error occurred";
      toast.error("Error deleting selected items: " + errorMessage);
    } finally {
      setIsDeletingSelected(false);
      setIsDeleting(false);
      setShowDeleteModal(false);
      setDeleteMode(null);
    }
  };

  // New function to handle delete all items
  const handleDeleteAllItems = async () => {
    try {
      setIsDeletingAll(true);
      setIsDeleting(true);

      const deletePromises = filteredItems.map(async (item) => {
        try {
          await api.delete(`/products/${item.product_id}`);
          return {
            success: true,
            product_id: item.product_id,
            name: item.product_name,
          };
        } catch (error) {
          return {
            success: false,
            product_id: item.product_id,
            name: item.product_name,
            error: error.response?.data?.message || error.message,
          };
        }
      });

      const results = await Promise.all(deletePromises);
      const failed = results.filter((r) => !r.success);
      const succeeded = results.filter((r) => r.success);

      if (succeeded.length > 0) {
        toast.success(`${succeeded.length} item(s) deleted successfully`);
      }

      if (failed.length > 0) {
        failed.forEach((f) => {
          toast.error(`Failed to delete "${f.name}": ${f.error}`);
        });
      }

      setSelectedItems([]);
      fetchItems();
    } catch (error) {
      console.error("Error deleting all items:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Unknown error occurred";
      toast.error("Error deleting all items: " + errorMessage);
    } finally {
      setIsDeletingAll(false);
      setIsDeleting(false);
      setShowDeleteModal(false);
      setDeleteMode(null);
    }
  };

  // Toggle item selection
  const toggleItemSelection = (product_id) => {
    setSelectedItems((prevSelected) => {
      if (prevSelected.includes(product_id)) {
        return prevSelected.filter((id) => id !== product_id);
      } else {
        return [...prevSelected, product_id];
      }
    });
  };

  // Toggle select all items on current page
  const toggleSelectAll = () => {
    const currentItems = filteredItems.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    );
    const currentItemIds = currentItems.map((item) => item.product_id);
    const allSelected = currentItemIds.every((id) =>
      selectedItems.includes(id)
    );
    if (allSelected) {
      // Unselect all
      setSelectedItems((prevSelected) =>
        prevSelected.filter((id) => !currentItemIds.includes(id))
      );
    } else {
      // Select all
      setSelectedItems((prevSelected) => [
        ...new Set([...prevSelected, ...currentItemIds]),
      ]);
    }
  };

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  const totalItems = filteredItems.length;
  const totalOpeningQty = filteredItems.reduce(
    (sum, item) => sum + (item.opening_stock_quantity || 0),
    0
  );
  const totalOpeningCost = filteredItems.reduce(
    (sum, item) =>
      sum + (item.opening_stock_quantity || 0) * (item.buying_cost || 0),
    0
  );
  const totalSellingPrice = filteredItems.reduce(
    (sum, item) =>
      sum + (item.opening_stock_quantity || 0) * (item.sales_price || 0),
    0
  );
  const profitMargin = totalSellingPrice - totalOpeningCost;
  const profitMarginPercentage = totalOpeningCost
    ? ((profitMargin / totalOpeningCost) * 100).toFixed(2)
    : 0;

  const formatNumber = (number) => {
    // Handle null, undefined, or empty values
    if (number === null || number === undefined || number === "") {
      return "-"; // Show dash for empty values
    }

    // Convert to number if it's a string
    const num = typeof number === "string" ? parseFloat(number) : number;

    // Check if it's a valid number
    if (isNaN(num)) {
      return "-";
    }

    return num.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      <ToastContainer />
      <div className="flex items-center justify-between">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 p-4 bg-white rounded-xl shadow-sm">
          {/* Action Buttons Group */}
          <div className="flex flex-wrap items-center justify-end gap-3 w-full md:w-auto">
            {/* Add Item Button */}
            <button
              onClick={() => {
                setSelectedItem(null);
                setIsBatchMode(false);
                setShowForm(true);
              }}
              className="flex items-center gap-2 px-6 py-2.5 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
            >
              <Plus className="w-5 h-5" />
              <span className="whitespace-nowrap">Add Item</span>
            </button>

            {/* Unified Search Bar */}
            <div className="relative flex-1 max-w-md w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="w-5 h-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-transparent focus:outline-none transition-all duration-200 shadow-sm"
                placeholder="Search by name, code, barcode, batch, category..."
              />
            </div>
            {/* File Input */}
            <div className="relative">
              <input
                type="file"
                onChange={handleFileSelect}
                className="hidden"
                id="fileInput"
                accept=".xlsx,.xls,.csv"
              />
              <button
                onClick={() => document.getElementById("fileInput").click()}
                className="flex items-center gap-2 px-5 py-2.5 text-white bg-emerald-600 rounded-lg hover:bg-emerald-700 transition-all duration-200 shadow-sm hover:shadow-md focus:ring-2 focus:ring-emerald-400 focus:ring-opacity-50 whitespace-nowrap"
              >
                <Upload className="w-5 h-5" />
                Select Excel File
              </button>
            </div>

            {/* Import Button */}
            <button
              onClick={handleImport}
              disabled={!selectedFile || isImporting}
              className={`flex items-center gap-2 px-5 py-2.5 text-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:ring-2 focus:ring-opacity-50 whitespace-nowrap ${
                !selectedFile || isImporting
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-400"
              }`}
            >
              {isImporting ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Download className="w-5 h-5" />
              )}
              {isImporting ? "Importing..." : "Import"}
            </button>

            {/* Export Button */}
            <button
              onClick={() => handleExport(items, setIsExporting, toast)}
              disabled={isExporting}
              className={`flex items-center gap-2 px-5 py-2.5 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md focus:ring-2 focus:ring-indigo-400 focus:ring-opacity-50 whitespace-nowrap ${
                isExporting ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isExporting ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Upload className="w-5 h-5" />
              )}
              {isExporting ? "Exporting..." : "Export"}
            </button>

            {/* Delete Selected */}
            <button
              onClick={() => {
                setDeleteMode("selected");
                setShowDeleteModal(true);
              }}
              disabled={selectedItems.length === 0 || isDeletingSelected}
              className={`flex items-center gap-2 px-5 py-2.5 text-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:ring-2 focus:ring-opacity-50 whitespace-nowrap ${
                selectedItems.length === 0 || isDeletingSelected
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-rose-600 hover:bg-rose-700 focus:ring-rose-400"
              }`}
            >
              {isDeletingSelected ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Trash2 className="w-5 h-5" />
              )}
              Delete Selected
            </button>

            {/* Delete All */}
            <button
              onClick={() => {
                setDeleteMode("all");
                setShowDeleteModal(true);
              }}
              disabled={filteredItems.length === 0 || isDeletingAll}
              className={`flex items-center gap-2 px-5 py-2.5 text-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:ring-2 focus:ring-opacity-50 whitespace-nowrap ${
                filteredItems.length === 0 || isDeletingAll
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-rose-800 hover:bg-rose-900 focus:ring-rose-500"
              }`}
            >
              {isDeletingAll ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Trash2 className="w-5 h-5" />
              )}
              Delete All
            </button>
          </div>
        </div>
      </div>

      {showDeleteModal && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setDeleteMode(null);
            setSelectedItem(null);
          }}
          onConfirm={() => {
            if (deleteMode === "selected") {
              handleDeleteSelectedItems();
            } else if (deleteMode === "all") {
              handleDeleteAllItems();
            } else if (deleteMode === "single" && selectedItem) {
              handleDeleteItem(selectedItem.product_id);
            }
          }}
          message={
            deleteMode === "selected"
              ? `Are you sure you want to delete ${selectedItems.length} selected item(s)?`
              : deleteMode === "all"
                ? "Are you sure you want to delete all items?"
                : deleteMode === "single" && selectedItem
                  ? `Are you sure you want to delete "${selectedItem.product_name}"?`
                  : "Are you sure you want to delete this item?"
          }
        />
      )}
      {isDeleting && (
        <ProgressOverlay indeterminate={true} message="Deleting items..." />
      )}
      {showForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-lg p-6 bg-white rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900">
              {selectedItem ? (isBatchMode ? "Add Batch" : "Edit Item") : "Add New Item"}
            </h2>
            <ItemForm
              onSubmit={handleAddItem}
              initialData={selectedItem}
              isBatchOnly={isBatchMode}
              onClose={() => {
                setShowForm(false);
                setSelectedItem(null);
                setIsBatchMode(false);
              }}
            />
            {console.log("ItemForm props:", { selectedItem, isBatchMode })}
          </div>
        </div>
      )}

      {showDetailsModal && (
        <ProductDetailsModal
          productId={selectedItem?.product_id}
          onClose={() => setShowDetailsModal(false)}
        />
      )}

      {isImporting && (
        <ProgressOverlay
          progress={uploadProgress}
          indeterminate={false}
          message="Uploading files..."
        />
      )}

      <div className="overflow-x-auto border rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="sticky top-0 text-white bg-gray-700">
            <tr>
              <th className="p-2 text-xs text-center uppercase">
                <input
                  type="checkbox"
                  onChange={toggleSelectAll}
                  checked={
                    filteredItems.length > 0 &&
                    filteredItems
                      .slice(
                        (currentPage - 1) * itemsPerPage,
                        currentPage * itemsPerPage
                      )
                      .every((item) => selectedItems.includes(item.product_id))
                  }
                />
              </th>
              <th className="p-2 text-xs text-center uppercase">No</th>
              <th className="p-2 text-xs text-center uppercase">Name</th>
              <th className="p-2 text-xs text-center uppercase">Category</th>
              <th className="p-2 text-xs text-center uppercase">Batch</th> {/* New column */}
              <th className="p-2 text-xs text-center uppercase">
                Buying Price
              </th>
              <th className="p-2 text-xs text-center uppercase">
                Selling Price
              </th>
              <th className="p-2 text-xs text-center uppercase">Opening Qty</th>
              <th className="p-2 text-xs text-center uppercase">
                Opening Value
              </th>
              <th className="p-2 text-xs text-center uppercase">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 max-h-[400px] overflow-y-auto">
            {loading ? (
              <tr>
                <td colSpan="12" className="p-4 text-center text-gray-500">
                  Loading...
                </td>
              </tr>
            ) : filteredItems.length > 0 ? (
              filteredItems
                .slice(
                  (currentPage - 1) * itemsPerPage,
                  currentPage * itemsPerPage
                )
                .map((item, index) => (
                  <tr
                    key={item.product_id}
                    className="hover:bg-gray-500 hover:text-emerald-300"
                  >
                    <td className="px-4 py-2 text-xs text-center">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.product_id)}
                        onChange={() => toggleItemSelection(item.product_id)}
                      />
                    </td>
                    <td className="px-4 py-2 text-xs text-center">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </td>
                    <td className="px-4 py-2 text-xs text-left">
                      {item.product_name}
                    </td>
                    <td className="px-4 py-2 text-xs text-center">
                      {item.category}
                    </td>
                    <td className="px-4 py-2 text-xs text-center">
                      {/* Show first variant's batch number, or item.batch_number, or '-' */}
                      {item.variants && item.variants.length > 0
                        ? item.variants[0].batch_number || "-"
                        : item.batch_number || "-"}
                    </td>
                    <td className="px-4 py-2 text-xs text-right">
                      LKR{" "}
                      {formatNumber(
                        item.variants && item.variants[0]
                          ? item.variants[0].buying_cost
                          : 0
                      )}
                    </td>
                    <td className="px-4 py-2 text-xs text-right">
                      LKR{" "}
                      {formatNumber(
                        item.variants && item.variants[0]
                          ? item.variants[0].sales_price
                          : 0
                      )}
                    </td>
                    <td className="px-4 py-2 text-xs text-right">
                      {item.variants && item.variants[0]
                        ? item.variants[0].opening_stock_quantity
                        : 0}
                    </td>
                    <td className="px-4 py-2 text-xs text-right">
                      LKR{" "}
                      {formatNumber(
                        (item.variants && item.variants[0]
                          ? item.variants[0].opening_stock_quantity
                          : 0) *
                          (item.variants && item.variants[0]
                            ? item.variants[0].buying_cost
                            : 0)
                      )}
                    </td>
                    <td className="flex justify-center gap-2 p-2">
                      <button
                        onClick={() => {
                          setSelectedItem(item);
                          setShowDetailsModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditItem(item)}
                        className="text-green-600 hover:text-green-900"
                      >
                        <FiEdit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedItem(item);
                          setDeleteMode("single");
                          setShowDeleteModal(true);
                        }}
                        className="text-red-600 hover:text-red-900"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))
            ) : (
              <tr>
                <td colSpan="12" className="p-4 text-center text-gray-500">
                  No items found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        currentPage={currentPage}
        totalItems={filteredItems.length}
        itemsPerPage={itemsPerPage}
        paginate={paginate}
      />

      <div className="p-4 mt-4 text-center bg-transparent rounded-lg shadow-lg">
        <h2 className="mb-4 text-xl font-bold">Summary</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          <div className="p-4 rounded-lg bg-cyan-800">
            <p className="text-sm text-cyan-500">Total Items</p>
            <p className="text-2xl font-bold text-cyan-300">{totalItems}</p>
          </div>
          <div className="p-4 rounded-lg bg-rose-800">
            <p className="text-sm text-pink-500">Total Opening Qty</p>
            <p className="text-2xl font-bold text-pink-300">
              {totalOpeningQty}
            </p>
          </div>
          <div className="p-4 rounded-lg bg-lime-800">
            <p className="text-sm text-lime-500">Total Opening Cost</p>
            <p className="text-2xl font-bold text-lime-300">
              LKR {formatNumber(totalOpeningCost)}
            </p>
          </div>
          <div className="p-4 rounded-lg bg-fuchsia-800">
            <p className="text-sm text-fuchsia-500">Total Selling Price</p>
            <p className="text-2xl font-bold text-fuchsia-300">
              LKR {formatNumber(totalSellingPrice)}
            </p>
          </div>
          <div className="p-4 bg-purple-800 rounded-lg">
            <p className="text-sm text-purple-500">Profit Margin</p>
            <p className="text-2xl font-bold text-purple-300">
              {profitMarginPercentage}%
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Items;
