import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, FiSearch, FiEdit, FiTrash2, <PERSON><PERSON><PERSON>, FiX } from "react-icons/fi";
import { FaBook } from "react-icons/fa";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { motion, AnimatePresence } from "framer-motion";
import { LedgerForm } from "./LedgerForm";
import axios from "axios";

const NewLedger = () => {
  const [ledgers, setLedgers] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [filterType, setFilterType] = useState("name");
  const [searchQuery, setSearchQuery] = useState("");
  const [editId, setEditId] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedLedger, setSelectedLedger] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  const API_URL = "http://localhost:8000/api/staff-ledger";

  // Fetch ledgers from backend
  const fetchLedgers = async () => {
    try {
      const response = await axios.get(API_URL, {
        params: { filter_type: filterType, search_query: searchQuery },
      });
      setLedgers(response.data.data);
    } catch (error) {
      toast.error("Failed to fetch ledgers");
      console.error(error);
    }
  };

  useEffect(() => {
    fetchLedgers();
  }, [filterType, searchQuery]);

  const handleAddLedger = async (formData) => {
    try {
      const data = new FormData();
      Object.keys(formData).forEach((key) => {
        if (formData[key]) data.append(key, formData[key]);
      });

      if (editId) {
        // Update existing ledger
        const response = await axios.post(`${API_URL}/${editId}`, data, {
          headers: { "Content-Type": "multipart/form-data" },
        });
        toast.success(response.data.message);
      } else {
        // Create new ledger
        const response = await axios.post(API_URL, data, {
          headers: { "Content-Type": "multipart/form-data" },
        });
        toast.success(response.data.message);
      }
      setShowForm(false);
      setEditId(null);
      fetchLedgers();
    } catch (error) {
      toast.error(error.response?.data?.message || "Operation failed");
      console.error(error);
    }
  };

  const handleEdit = async (ledgerId) => {
    try {
      const response = await axios.get(`${API_URL}/${ledgerId}`);
      setEditId(ledgerId);
      setShowForm(true);
    } catch (error) {
      toast.error("Failed to fetch ledger details");
      console.error(error);
    }
  };

  const handleDelete = (ledgerId) => {
    setDeleteId(ledgerId);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      const response = await axios.delete(`${API_URL}/${deleteId}`);
      toast.success(response.data.message);
      setShowDeleteModal(false);
      setDeleteId(null);
      fetchLedgers();
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to delete ledger");
      console.error(error);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDeleteId(null);
  };

  const handleView = async (ledgerId) => {
    try {
      const response = await axios.get(`${API_URL}/${ledgerId}`);
      setSelectedLedger(response.data.data);
      setShowViewModal(true);
    } catch (error) {
      toast.error("Failed to fetch ledger details");
      console.error(error);
    }
  };

  const closeViewModal = () => {
    setShowViewModal(false);
    setSelectedLedger(null);
  };

  // Framer Motion variants for modals
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.7 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, scale: 0.7, transition: { duration: 0.3 } },
  };

  const deleteModalVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, transition: { duration: 0.3 } },
  };

  return (
    <div className="flex flex-col min-h-screen p-4 bg-transparent">
      {/* Header */}
      <div className="py-3 mb-6 text-center text-white rounded-lg shadow-md bg-gradient-to-r from-blue-500 to-blue-800 dark:bg-gradient-to-r dark:from-blue-900 dark:to-slate-800">
        <h1 className="text-2xl font-bold">LEDGER MANAGEMENT</h1>
        <p className="text-sm opacity-90">Manage your ledger accounts efficiently</p>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => {
              setEditId(null);
              setShowForm(true);
            }}
            className="flex items-center gap-2 px-4 py-2 text-sm text-white bg-blue-600 rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            <FaBook /> Add Ledger
          </button>

          <div className="relative flex items-center gap-2">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-gray-900 dark:border-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="staff_id">Staff ID</option>
              <option value="name">Name</option>
              <option value="account_group">Account Group</option>
              <option value="mobile_no">Mobile No</option>
              <option value="email">Email</option>
            </select>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={`Search by ${filterType
                  .replace(/([A-Z])/g, " $1")
                  .replace(/^./, (str) => str.toUpperCase())}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full max-w-md py-2 pl-10 pr-3 bg-white border border-gray-300 rounded-lg shadow-sm dark:bg-gray-900 dark:border-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Form Modal */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            className="fixed inset-0 z-[60] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={modalVariants}
          >
            <motion.div
              className="relative w-full max-w-4xl max-h-[80vh] p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800 overflow-y-auto"
              variants={modalVariants}
            >
              <LedgerForm
                onSubmit={async (formData) => {
                  try {
                    await handleAddLedger(formData);
                  } catch (error) {
                    if (error.response?.data?.errors?.staff_id) {
                      toast.error("Staff ID already exists");
                    }
                  }
                }}
                onCancel={() => {
                  setShowForm(false);
                  setEditId(null);
                }}
                editId={editId}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* View Modal */}
      <AnimatePresence>
        {showViewModal && selectedLedger && (
          <motion.div
            className="fixed inset-0 z-[70] flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={modalVariants}
          >
            <motion.div
              className="relative w-full max-w-4xl p-8 bg-white rounded-2xl shadow-xl dark:bg-gray-900 dark:border-gray-700 max-h-[80vh] overflow-y-auto"
              variants={modalVariants}
            >
              <button
                onClick={closeViewModal}
                className="absolute text-gray-500 top-4 right-4 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
                aria-label="Close"
              >
                <FiX className="w-6 h-6" />
              </button>
              <h2 className="mb-8 text-2xl font-semibold text-gray-800 dark:text-white">
                Ledger Details
              </h2>
              <div className="space-y-8">
                {/* Profile Picture and Header */}
                <div className="flex items-center justify-between pb-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-4">
                    {selectedLedger.profile_picture ? (
                      <img
                        src={`http://localhost:8000/storage/${selectedLedger.profile_picture}`}
                        alt="Profile"
                        className="object-cover w-20 h-20 rounded-md shadow-sm"
                      />
                    ) : (
                      <div className="flex items-center justify-center w-20 h-20 text-gray-400 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-500">
                        <span className="text-sm">No Image</span>
                      </div>
                    )}
                    <div>
                      <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100">
                        {selectedLedger.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedLedger.account_group}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Staff ID: {selectedLedger.staff_id || "N/A"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Basic Information Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                    Basic Information
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Staff ID
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.staff_id || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Name
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.name || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Account Group
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.account_group || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Address
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.address || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Opening Balance
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.opening_balance ? `LKR ${selectedLedger.opening_balance}` : "N/A"}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                    Contact Information
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Mobile No
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.mobile_no || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        WhatsApp No
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.whatsapp_no || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Telephone No
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.telephone_no || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Email
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.email || "N/A"}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Information Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                    Additional Information
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        NIC
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.nic || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Position
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.position || "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Join Date
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.join_date ? new Date(selectedLedger.join_date).toISOString().split('T')[0] : "N/A"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Date
                      </label>
                      <div className="p-2 text-gray-600 bg-gray-100 rounded-md dark:bg-gray-700 dark:text-gray-300">
                        {selectedLedger.date ? new Date(selectedLedger.date).toISOString().split('T')[0] : "N/A"}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={closeViewModal}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Close
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteModal && (
          <motion.div
            className="fixed inset-0 z-[70] flex items-center justify-center p-4 bg-black bg-opacity-60 backdrop-blur-sm"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={deleteModalVariants}
          >
            <motion.div
              className="relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800"
              variants={deleteModalVariants}
            >
              <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                Confirm Deletion
              </h3>
              <p className="mb-6 text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to delete this ledger? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-2">
                <button
                  onClick={cancelDelete}
                  className="px-4 py-2 text-sm text-gray-700 bg-gray-200 rounded-md dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 text-sm text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Delete
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Ledger Table */}
      <div className="overflow-hidden bg-white border border-gray-200 rounded-lg shadow-md dark:bg-slate-800 dark:border-slate-700">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm divide-y divide-gray-200 dark:divide-slate-600">
            <thead className="text-xs tracking-wider text-gray-700 uppercase bg-gray-100 dark:bg-slate-700 dark:text-gray-300">
              <tr>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Staff ID</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Name</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Account Group</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Mobile No</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Email</th>
                <th className="px-4 py-3 font-semibold text-left whitespace-nowrap">Opening Balance</th>
                <th className="px-4 py-3 font-semibold text-right whitespace-nowrap">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 dark:bg-slate-800 dark:divide-slate-600">
              {ledgers.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                    No ledger data found.
                  </td>
                </tr>
              ) : (
                ledgers.map((ledger) => (
                  <tr key={ledger.id} className="transition-colors hover:bg-gray-50 dark:hover:bg-slate-700/50">
                    <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">{ledger.staff_id || "N/A"}</td>
                    <td className="px-4 py-3 font-medium text-blue-600 dark:text-blue-400 whitespace-nowrap">{ledger.name}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{ledger.account_group}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{ledger.mobile_no || "N/A"}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{ledger.email || "N/A"}</td>
                    <td className="px-4 py-3 text-gray-600 dark:text-gray-300 whitespace-nowrap">{ledger.opening_balance}</td>
                    <td className="px-4 py-3 text-right whitespace-nowrap">
                      <div className="flex items-center justify-end gap-x-3">
                        <button
                          onClick={() => handleView(ledger.id)}
                          title="View Ledger"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 focus:outline-none"
                        >
                          <FiEye size={16} />
                        </button>
                        <button
                          onClick={() => handleEdit(ledger.id)}
                          title="Edit Ledger"
                          className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 focus:outline-none"
                        >
                          <FiEdit size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(ledger.id)}
                          title="Delete Ledger"
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 focus:outline-none"
                        >
                          <FiTrash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default NewLedger;