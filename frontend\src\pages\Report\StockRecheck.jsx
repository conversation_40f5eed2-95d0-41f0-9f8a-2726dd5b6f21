import React, { useState, useEffect, useMemo, useCallback } from "react";
import axios from "axios";
import { FaRedo } from "react-icons/fa";
import { useAuth } from "../../context/NewAuthContext";
import { debounce } from "lodash"; // Ensure lodash is installed or use a custom debounce function

const StockRecheckDashboard = () => {
  const { user } = useAuth();
  const [showForm, setShowForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [items, setItems] = useState([]);
  const [locations, setLocations] = useState([]);
  const [formData, setFormData] = useState({
    itemCode: "",
    itemName: "",
    systemQty: 0,
    storeQty: "",
    location: "",
    remarks: "",
    updateActualStock: false,
    variant_id: null,
    batch_number: null,
    expiry_date: null,
    recheck_type: "product", // Add recheck type
  });
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [errors, setErrors] = useState({});
  const [savedItems, setSavedItems] = useState([]);
  const [stockRechecksMap, setStockRechecksMap] = useState({});
  const [successMessage, setSuccessMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  // Add state for stock report data
  const [stockReportData, setStockReportData] = useState([]);

  // Fetch data function
  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch products with variants
      const productResponse = await axios.get(
        "http://127.0.0.1:8000/api/products"
      );
      const productsData = Array.isArray(productResponse.data.data)
        ? productResponse.data.data
        : [];

      // Process products to include variant information
      const processedItems = productsData.map((p) => ({
        ...p,
        itemCode: p.item_code || p.product_id,
        itemName: p.product_name,
        systemQty: parseFloat(p.closing_stock_quantity || 0),
        location: p.store_location || "",
        variants: p.variants || [],
      }));
      setItems(processedItems);

      // Fetch stock rechecks
      const recheckResponse = await axios.get(
        "http://127.0.0.1:8000/api/stock-rechecks"
      );
      const map = {};
      const batchMap = {};
      if (Array.isArray(recheckResponse.data)) {
        recheckResponse.data.forEach((rec) => {
          if (rec.recheck_type === 'batch') {
            // For batch rechecks, create a key with batch information
            const batchKey = rec.item_code + "|" + rec.location + "|" + 
                           (rec.product_variant_id || rec.batch_number);
            batchMap[batchKey] = rec.corrected_closing_stock;
          } else {
            // For product-level rechecks, use the original logic
            map[rec.item_code + "|" + rec.location] = rec.corrected_closing_stock;
          }
        });
      }
      setStockRechecksMap({ product: map, batch: batchMap });

      // Fetch locations
      const locationResponse = await axios.get(
        "http://127.0.0.1:8000/api/locations"
      );
      setLocations(
        Array.isArray(locationResponse.data)
          ? locationResponse.data.map((loc) => loc.name || loc)
          : []
      );

      // Update saved items
      setSavedItems(
        Array.isArray(recheckResponse.data) ? recheckResponse.data : []
      );

    } catch (err) {
      console.error("Error fetching data:", err);
      setError("Failed to fetch data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on mount
  useEffect(() => {
    fetchData();
  }, []);

  // Fetch stock report data on mount
  useEffect(() => {
    const fetchStockReport = async () => {
      try {
        const response = await axios.get("http://127.0.0.1:8000/api/detailed-stock-reports");
        setStockReportData(Array.isArray(response.data) ? response.data : []);
      } catch (err) {
        console.error("Error fetching stock report data:", err);
      }
    };
    fetchStockReport();
  }, []);

  // Generate batch-wise product options
  const getBatchWiseOptions = useMemo(() => {
    if (!items || items.length === 0) return [];
    const options = [];
    items.forEach((product) => {
      if (product.variants && product.variants.length > 0) {
        product.variants.forEach((variant) => {
          const batchInfo = variant.batch_number
            ? ` (Batch: ${variant.batch_number})`
            : "";
          const expiryInfo = variant.expiry_date
            ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
            : "";
          options.push({
            ...product,
            ...variant,
            product_id: product.product_id,
            variant_id: variant.product_variant_id,
            itemCode:
              variant.item_code || product.item_code || product.product_id,
            itemName: product.product_name,
            display_name: `${product.product_name}${batchInfo}${expiryInfo}`,
            systemQty: parseFloat(variant.closing_stock_quantity || 0),
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            location: variant.store_location || product.store_location || "",
          });
        });
      } else {
        options.push({
          ...product,
          itemCode: product.item_code || product.product_id,
          itemName: product.product_name,
          display_name: product.product_name,
          systemQty: parseFloat(product.closing_stock_quantity || 0),
          batch_number: null,
          expiry_date: null,
          location: product.store_location || "",
        });
      }
    });
    return options;
  }, [items]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      if (!query) {
        setSearchResults([]);
        return;
      }
      const lowerTerm = query.toLowerCase();
      const filtered = getBatchWiseOptions.filter(
        (item) =>
          item.display_name.toLowerCase().includes(lowerTerm) ||
          item.itemCode.toLowerCase().includes(lowerTerm) ||
          (item.batch_number &&
            item.batch_number.toLowerCase().includes(lowerTerm))
      );
      setSearchResults(filtered.slice(0, 10));
    }, 300),
    [getBatchWiseOptions]
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  // Calculate current system quantity based on latest recheck and transactions
  const calculateCurrentSystemQuantity = async (item) => {
    if (!item) return 0;

    console.log('=== CALCULATING SYSTEM QUANTITY ===');
    console.log('Item:', item);
    console.log('Item code:', item.item_code || item.itemCode);
    console.log('Location:', item.location);
    console.log('Variant ID:', item.variant_id);
    console.log('Batch number:', item.batch_number);
    console.log('Recheck type:', item.variant_id ? 'batch' : 'product');

    try {
      // Call the API to get the current system quantity
      const response = await axios.get('http://127.0.0.1:8000/api/stock-rechecks/current-system-qty', {
        params: {
          item_code: item.item_code || item.itemCode,
          location: item.location,
          product_variant_id: item.variant_id || null,
          batch_number: item.batch_number || null,
          recheck_type: item.variant_id ? 'batch' : 'product'
        }
      });

      console.log('API Response:', response.data);

      if (response.data && response.data.current_system_qty !== undefined) {
        console.log('✅ Using API calculated system quantity:', response.data.current_system_qty);
        return response.data.current_system_qty;
      } else {
        console.log('❌ API response missing current_system_qty');
      }
    } catch (error) {
      console.error('❌ Error fetching current system quantity:', error);
      console.error('Error details:', error.response?.data);
    }

    console.log('🔄 Falling back to stored values...');

    // Fallback to stored values if API call fails
    if (item.variant_id) {
      // For batch/variant items
      const batchKey = `${item.item_code || item.itemCode}|${item.location}|${item.variant_id}`;
      const latestRecheck = stockRechecksMap?.batch?.[batchKey];
      
      console.log('Batch key:', batchKey);
      console.log('Latest recheck from map:', latestRecheck);
      console.log('Item closing stock quantity:', item.closing_stock_quantity);
      console.log('Item opening stock quantity:', item.opening_stock_quantity);
      
      if (latestRecheck !== undefined && latestRecheck !== null) {
        console.log('✅ Using recheck value:', latestRecheck);
        return latestRecheck;
      } else {
        const fallbackValue = parseFloat(item.closing_stock_quantity || item.opening_stock_quantity || 0);
        console.log('✅ Using fallback value:', fallbackValue);
        return fallbackValue;
      }
    } else {
      // For product-level items
      const productKey = `${item.item_code || item.itemCode}|${item.location}`;
      const latestRecheck = stockRechecksMap?.product?.[productKey];
      
      console.log('Product key:', productKey);
      console.log('Latest recheck from map:', latestRecheck);
      console.log('Item closing stock quantity:', item.closing_stock_quantity);
      console.log('Item opening stock quantity:', item.opening_stock_quantity);
      
      if (latestRecheck !== undefined && latestRecheck !== null) {
        console.log('✅ Using recheck value:', latestRecheck);
        return latestRecheck;
      } else {
        const fallbackValue = parseFloat(item.closing_stock_quantity || item.opening_stock_quantity || 0);
        console.log('✅ Using fallback value:', fallbackValue);
        return fallbackValue;
      }
    }
  };

  // Handle product selection
  const handleProductSelect = async (item) => {
    // Calculate the current system quantity
    const currentSystemQty = await calculateCurrentSystemQuantity(item);
    
    console.log('Selected item:', item);
    console.log('Calculated system quantity:', currentSystemQty);
    console.log('Stock rechecks map:', stockRechecksMap);

    setFormData((prev) => ({
      ...prev,
      itemCode: item.itemCode,
      itemName: item.itemName,
      systemQty: currentSystemQty,
      location: item.location || prev.location,
      variant_id: item.variant_id || null,
      batch_number: item.batch_number || null,
      expiry_date: item.expiry_date || null,
      recheck_type: item.variant_id ? "batch" : "product",
    }));
    setSearchQuery("");
    setSearchResults([]);
  };

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === "storeQty" && value !== "") {
      if (!/^\d*$/.test(value)) {
        return; // Ignore non-numeric input
      }
    }

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    setErrors((prev) => ({
      ...prev,
      [name]: "",
    }));

    setSuccessMessage("");
  };

  // Calculate quantity difference
  const quantityDifference =
    formData.storeQty !== "" && formData.systemQty !== null
      ? parseInt(formData.storeQty, 10) - parseInt(formData.systemQty, 10)
      : 0;

  // Validate form fields
  const validate = () => {
    const newErrors = {};
    if (!formData.itemCode.trim()) newErrors.itemCode = "Item Code is required";
    if (!formData.location.trim()) newErrors.location = "Location is required";
    if (formData.storeQty === "")
      newErrors.storeQty = "Store Quantity is required";
    return newErrors;
  };

  // Handle form submission
  const handleSave = async (e) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      setSuccessMessage("");
      return;
    }

    if (formData.updateActualStock) {
      if (
        !window.confirm(
          "This will update the closing stock in the stock report. Proceed?"
        )
      ) {
        return;
      }
    }

    setLoading(true);
    try {
      const newItem = {
        item_code: formData.itemCode,
        item_name: formData.itemName,
        system_qty: formData.systemQty,
        store_qty: parseInt(formData.storeQty, 10),
        difference: quantityDifference,
        location: formData.location,
        remarks: formData.remarks,
        status: quantityDifference === 0 ? "OK" : "Discrepancy",
        update_actual_stock: formData.updateActualStock,
        user_id: user?.id || null,
        product_variant_id: formData.variant_id || null, // Use correct field name
        batch_number: formData.batch_number || null,
        expiry_date: formData.expiry_date || null,
        recheck_type: formData.recheck_type,
      };

      let response;
      if (isEditing) {
        response = await axios.patch(
          `http://127.0.0.1:8000/api/stock-rechecks/${editingId}`,
          newItem
        );
      } else {
        response = await axios.post(
          "http://127.0.0.1:8000/api/stock-rechecks",
          newItem
        );
      }

      await fetchData();
      setSuccessMessage(
        response.data.message ||
          (isEditing
            ? "Stock recheck updated successfully!"
            : "Stock recheck saved successfully!")
      );

      // Dispatch a global event so StockReport can refresh
      window.dispatchEvent(new Event('stockRecheckUpdated'));

      setFormData({
        itemCode: "",
        itemName: "",
        systemQty: 0,
        storeQty: "",
        location: "",
        remarks: "",
        updateActualStock: false,
        variant_id: null,
        batch_number: null,
        expiry_date: null,
        recheck_type: "product",
      });
      setEditingId(null);
      setIsEditing(false);
      setShowForm(false);
    } catch (err) {
      console.error("Error saving stock recheck:", err);
      setError(
        err.response?.data?.error ||
          (isEditing
            ? "Failed to update stock recheck."
            : "Failed to save stock recheck.") + " Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    setFormData({
      itemCode: "",
      itemName: "",
      systemQty: 0,
      storeQty: "",
      location: "",
      remarks: "",
      updateActualStock: false,
      variant_id: null,
      batch_number: null,
      expiry_date: null,
      recheck_type: "product",
    });
    setErrors({});
    setSuccessMessage("");
    setEditingId(null);
    setIsEditing(false);
    setShowForm(false);
    setSearchQuery("");
    setSearchResults([]);
  };

  // Handle view button
  const handleView = (item) => {
    setSelectedItem(item);
    setShowViewModal(true);
  };

  // Close view modal
  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedItem(null);
  };

  // Highlight rows with major discrepancies
  const isMajorDiscrepancy = (diff) => diff > 10 || diff < -10;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6 transition-colors duration-500">
      {/* Dashboard Header */}
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-extrabold text-gray-900 dark:text-gray-100">
          Stock Recheck Dashboard
        </h1>
        <button
          onClick={() => {
            setShowForm(true);
            setIsEditing(false);
            setEditingId(null);
            setFormData({
              itemCode: "",
              itemName: "",
              systemQty: 0,
              storeQty: "",
              location: "",
              remarks: "",
              updateActualStock: false,
              variant_id: null,
              batch_number: null,
              expiry_date: null,
              recheck_type: "product",
            });
            setSearchQuery("");
            setSearchResults([]);
          }}
          className="flex items-center space-x-2 bg-red-600 hover:bg-yellow-500 dark:bg-red-700 dark:hover:bg-yellow-600 text-black font-semibold py-2 px-4 rounded-full shadow-md transition-colors duration-300 fixed bottom-8 right-8 z-50"
          aria-label="Recheck Stock"
          title="Recheck Stock"
        >
          <FaRedo />
          <span>Recheck Stock</span>
        </button>
      </header>

      {/* Error Message */}
      {error && (
        <div className="w-full max-w-6xl mb-4 p-4 bg-red-100 text-red-800 rounded-lg shadow-md flex items-center justify-between">
          <span>{error}</span>
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
          >
            Retry
          </button>
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="w-full max-w-6xl mb-4 p-4 bg-green-100 text-green-800 rounded-lg shadow-md">
          {successMessage}
        </div>
      )}

      {/* Stock Recheck Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex justify-center items-start pt-20 z-50 overflow-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl p-6 mx-4">
            <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
              Stock Recheck Form
            </h2>
            <form
              onSubmit={handleSave}
              noValidate
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-4xl transition-all duration-300 ease-in-out transform hover:shadow-2xl"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Item Code with search */}
                <div className="md:col-span-2 relative transition-all duration-200 ease-in-out">
                  <label
                    htmlFor="itemCode"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    Item Code
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="itemCode"
                      name="itemCode"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      autoComplete="off"
                      className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-300 ${
                        errors.itemCode
                          ? "border-red-500 focus:ring-red-500 animate-pulse"
                          : "border-gray-300 dark:border-gray-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                      } bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                      placeholder="Search by name, code, or batch..."
                    />
                    {searchQuery && searchResults.length > 0 && (
                      <ul className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg overflow-hidden transition-all duration-300 origin-top transform scale-y-100 opacity-100 max-h-60 overflow-y-auto">
                        {searchResults.map((item, index) => (
                          <li
                            key={`${item.itemCode}-${item.variant_id || index}`}
                            className="px-4 py-3 cursor-pointer hover:bg-blue-500 hover:text-white dark:hover:bg-blue-600 transition-colors duration-150"
                            onClick={() => handleProductSelect(item)}
                          >
                            <div className="flex flex-col">
                              <div className="font-medium">
                                {item.itemName}
                                {item.batch_number && (
                                  <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 text-xs rounded-full font-semibold">
                                    Batch: {item.batch_number}
                                  </span>
                                )}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Code: {item.itemCode} | Stock: {item.systemQty}{" "}
                                | Location: {item.location}
                                {item.expiry_date &&
                                  ` | Exp: ${item.expiry_date.split("T")[0]}`}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  {errors.itemCode && (
                    <p className="mt-1 text-red-500 text-sm animate-bounce">
                      {errors.itemCode}
                    </p>
                  )}
                </div>

                {/* Item Name */}
                <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label
                    htmlFor="itemName"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    Item Name
                  </label>
                  <input
                    type="text"
                    id="itemName"
                    name="itemName"
                    value={formData.itemName}
                    readOnly
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed transition-colors duration-200"
                    placeholder="Auto-filled"
                  />
                </div>

                {/* System Stock Quantity */}
                <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label
                    htmlFor="systemQty"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    System Stock Quantity
                  </label>
                  <input
                    type="number"
                    id="systemQty"
                    name="systemQty"
                    value={formData.systemQty}
                    readOnly
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed transition-colors duration-200"
                    placeholder="Auto-filled"
                  />
                </div>

                {/* Store Stock Quantity */}
                <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label
                    htmlFor="storeQty"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    Store Stock Quantity
                  </label>
                  <input
                    type="text"
                    id="storeQty"
                    name="storeQty"
                    value={formData.storeQty}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.storeQty
                        ? "border-red-500 focus:ring-red-500 animate-pulse"
                        : "border-gray-300 dark:border-gray-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                    } bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="Enter store stock quantity"
                    inputMode="numeric"
                  />
                  {errors.storeQty && (
                    <p className="mt-1 text-red-500 text-sm animate-bounce">
                      {errors.storeQty}
                    </p>
                  )}
                </div>

                {/* Quantity Difference */}
                <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200">
                    Quantity Difference
                  </label>
                  <input
                    type="number"
                    value={quantityDifference}
                    readOnly
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                      quantityDifference === 0
                        ? "border-green-500 text-green-700 dark:text-green-400 dark:border-green-400"
                        : "border-red-500 text-red-700 dark:text-red-400 dark:border-red-400"
                    } bg-gray-100 dark:bg-gray-600 cursor-not-allowed`}
                  />
                </div>

                {/* Location Dropdown */}
                <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label
                    htmlFor="location"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    Location / Store
                  </label>
                  <select
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-300 ${
                      errors.location
                        ? "border-red-500 focus:ring-red-500 animate-pulse"
                        : "border-gray-300 dark:border-gray-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                    } bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                  >
                    <option value="">Select location</option>
                    {locations.map((loc) => (
                      <option key={loc} value={loc}>
                        {loc}
                      </option>
                    ))}
                  </select>
                  {errors.location && (
                    <p className="mt-1 text-red-500 text-sm animate-bounce">
                      {errors.location}
                    </p>
                  )}
                </div>

                {/* Recheck Type */}
                <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label
                    htmlFor="recheck_type"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    Recheck Type
                  </label>
                  <select
                    id="recheck_type"
                    name="recheck_type"
                    value={formData.recheck_type}
                    onChange={handleChange}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-300"
                  >
                    <option value="product">Product Level</option>
                    <option value="batch">Batch Level</option>
                  </select>
                </div>

                {/* Batch Information Display */}
                {formData.recheck_type === "batch" && (formData.batch_number || formData.variant_id) && (
                  <div className="transition-all duration-200 ease-in-out hover:scale-[1.01]">
                    <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200">
                      Batch Information
                    </label>
                    <div className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300">
                      {formData.batch_number && <div>Batch: {formData.batch_number}</div>}
                      {formData.expiry_date && <div>Expiry: {formData.expiry_date.split("T")[0]}</div>}
                      {formData.variant_id && <div>Variant ID: {formData.variant_id}</div>}
                    </div>
                  </div>
                )}

                {/* Remarks */}
                <div className="md:col-span-2 transition-all duration-200 ease-in-out hover:scale-[1.01]">
                  <label
                    htmlFor="remarks"
                    className="block text-gray-700 dark:text-gray-300 font-medium mb-1 transition-colors duration-200"
                  >
                    Remarks
                  </label>
                  <textarea
                    id="remarks"
                    name="remarks"
                    value={formData.remarks}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-300 resize-none"
                    placeholder="Enter remarks (optional)"
                  />
                </div>

                {/* Checkbox */}
                <div className="md:col-span-2 transition-all duration-200 ease-in-out">
                  <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-300">
                    <input
                      type="checkbox"
                      id="updateActualStock"
                      name="updateActualStock"
                      checked={formData.updateActualStock}
                      onChange={handleChange}
                      className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
                    />
                    <label
                      htmlFor="updateActualStock"
                      className="text-gray-700 dark:text-gray-300 font-medium transition-colors duration-200"
                    >
                      Update closing stock count in stock report
                    </label>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="md:col-span-2 mt-8 flex justify-end space-x-4 transition-all duration-300">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="bg-gray-300 hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:-translate-y-1 transform"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:-translate-y-1 transform hover:shadow-lg"
                  disabled={loading}
                >
                  {loading
                    ? "Saving..."
                    : isEditing
                      ? "Update Changes"
                      : "Save Changes"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* After the Stock Recheck Form Modal, add a debug table for stockReportData */}
      {showForm && (
        <div className="mt-8 bg-yellow-50 border border-yellow-300 rounded-lg p-4 overflow-x-auto">
          <h3 className="text-lg font-bold mb-2 text-yellow-800">[DEBUG] Stock Report Data</h3>
          <table className="min-w-full text-xs text-left">
            <thead>
              <tr>
                {stockReportData.length > 0 && Object.keys(stockReportData[0]).map((key) => (
                  <th key={key} className="px-2 py-1 border-b border-yellow-300 text-yellow-900">{key}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {stockReportData.map((row, idx) => (
                <tr key={idx} className="border-b border-yellow-200">
                  {Object.values(row).map((val, i) => (
                    <td key={i} className="px-2 py-1 text-yellow-800">{String(val)}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          <div className="text-xs text-yellow-700 mt-2">This table is for debugging only. It shows all rows returned by /api/detailed-stock-reports. Use it to verify what data is available for your selection.</div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex justify-center items-start pt-20 z-50 overflow-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl p-6 mx-4">
            <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
              Stock Recheck Details
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Item Code
                </label>
                <input
                  type="text"
                  value={selectedItem.item_code}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Item Name
                </label>
                <input
                  type="text"
                  value={selectedItem.item_name}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Batch Number
                </label>
                <input
                  type="text"
                  value={selectedItem.batch_number || "No Batch"}
                  readOnly
                  className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 cursor-not-allowed ${
                    selectedItem.batch_number 
                      ? 'text-blue-600 dark:text-blue-400 font-semibold' 
                      : 'text-gray-500 dark:text-gray-400 italic'
                  }`}
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  System Quantity
                </label>
                <input
                  type="number"
                  value={selectedItem.system_qty}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Store Quantity
                </label>
                <input
                  type="number"
                  value={selectedItem.store_qty}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Quantity Difference
                </label>
                <input
                  type="number"
                  value={selectedItem.difference}
                  readOnly
                  className={`w-full px-4 py-3 rounded-lg border bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed ${
                    selectedItem.difference === 0
                      ? "border-green-500"
                      : "border-red-500"
                  }`}
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Location
                </label>
                <input
                  type="text"
                  value={selectedItem.location}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Status
                </label>
                <input
                  type="text"
                  value={selectedItem.status}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  User
                </label>
                <input
                  type="text"
                  value={selectedItem.user?.name || "Unknown"}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Remarks
                </label>
                <textarea
                  value={selectedItem.remarks || "No remarks provided"}
                  readOnly
                  rows={3}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed resize-none"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Date
                </label>
                <input
                  type="text"
                  value={
                    new Date(selectedItem.created_at).toLocaleString() ||
                    "Unknown"
                  }
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Recheck Type
                </label>
                <input
                  type="text"
                  value={selectedItem.recheck_type === 'batch' ? 'Batch Level' : 'Product Level'}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                  Update Closing Stock
                </label>
                <input
                  type="text"
                  value={selectedItem.update_actual_stock ? "Yes" : "No"}
                  readOnly
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-not-allowed"
                />
              </div>
              
              {/* Show batch details if it's a batch recheck */}
              {selectedItem.recheck_type === 'batch' && (selectedItem.batch_number || selectedItem.product_variant_id) && (
                <div className="md:col-span-2">
                  <label className="block text-gray-700 dark:text-gray-300 font-medium mb-1">
                    Batch Details
                  </label>
                  <div className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300">
                    {selectedItem.batch_number && <div>Batch Number: {selectedItem.batch_number}</div>}
                    {selectedItem.product_variant_id && <div>Variant ID: {selectedItem.product_variant_id}</div>}
                    {selectedItem.expiry_date && <div>Expiry Date: {selectedItem.expiry_date.split("T")[0]}</div>}
                  </div>
                </div>
              )}
            </div>
            <div className="mt-8 flex justify-end">
              <button
                onClick={handleCloseViewModal}
                className="bg-gray-300 hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:-translate-y-1 transform"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Total Rechecks</h3>
          <p className="text-2xl font-bold">{savedItems.length}</p>
        </div>
        <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Product Level</h3>
          <p className="text-2xl font-bold">
            {savedItems.filter(item => item.recheck_type !== 'batch').length}
          </p>
        </div>
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Batch Level</h3>
          <p className="text-2xl font-bold">
            {savedItems.filter(item => item.recheck_type === 'batch').length}
          </p>
        </div>
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-lg text-white">
          <h3 className="text-sm font-medium opacity-90">Discrepancies</h3>
          <p className="text-2xl font-bold">
            {savedItems.filter(item => item.status === 'Discrepancy').length}
          </p>
        </div>
      </div>

      {/* Rechecked Items Table */}
      <div className="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-600 dark:bg-gray-700">
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Item Code
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Item Name
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Batch Number
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                System Qty
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Store Qty
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Difference
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Location
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Type
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                User
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-white dark:text-gray-300 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-600 dark:divide-gray-600">
            {loading ? (
              <tr>
                <td
                  colSpan="11"
                  className="px-4 py-6 text-center text-gray-600 dark:text-gray-400"
                >
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto"></div>
                </td>
              </tr>
            ) : savedItems.length === 0 ? (
              <tr>
                <td
                  colSpan="11"
                  className="px-4 py-6 text-center text-gray-600 dark:text-gray-400"
                >
                  No rechecked items found.
                </td>
              </tr>
            ) : (
              savedItems.map((item) => (
                <tr
                  key={item.id}
                  className={
                    isMajorDiscrepancy(item.difference)
                      ? "bg-red-100 dark:bg-red-900"
                      : item.difference !== 0
                        ? "bg-yellow-100 dark:bg-yellow-900"
                        : "bg-white dark:bg-gray-800"
                  }
                >
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.item_code}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.item_name}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.batch_number ? (
                      <div className="flex flex-col">
                        <span className="font-semibold text-blue-600 dark:text-blue-400">
                          {item.batch_number}
                        </span>
                        {item.expiry_date && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            Exp: {item.expiry_date.split("T")[0]}
                          </span>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400 dark:text-gray-500 italic">
                        No Batch
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.system_qty}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.store_qty}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.difference}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.location}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.recheck_type === 'batch' 
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {item.recheck_type === 'batch' ? 'Batch' : 'Product'}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {item.status}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                    {item.user?.name || "Unknown"}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100 flex space-x-2">
                    <button
                      onClick={() => handleView(item)}
                      className="text-green-600 hover:text-green-800 dark:hover:text-green-400 font-semibold"
                    >
                      View
                    </button>
                    <button
                      onClick={() => {
                        setFormData({
                          itemCode: item.item_code,
                          itemName: item.item_name,
                          systemQty: item.system_qty,
                          storeQty: item.store_qty.toString(),
                          location: item.location,
                          remarks: item.remarks || "",
                          updateActualStock: item.update_actual_stock,
                          variant_id: item.product_variant_id || null,
                          batch_number: item.batch_number || null,
                          expiry_date: item.expiry_date || null,
                          recheck_type: item.recheck_type || "product",
                        });
                        setEditingId(item.id);
                        setIsEditing(true);
                        setShowForm(true);
                        setSearchQuery(
                          item.batch_number
                            ? `${item.item_name} (Batch: ${item.batch_number})`
                            : item.item_name
                        );
                      }}
                      className="text-blue-600 hover:text-blue-800 dark:hover:text-blue-400 font-semibold"
                    >
                      Edit
                    </button>
                    <button
                      onClick={async () => {
                        if (
                          window.confirm(
                            "Are you sure you want to delete this stock recheck?"
                          )
                        ) {
                          try {
                            const response = await fetch(
                              `http://127.0.0.1:8000/api/stock-rechecks/${item.id}`,
                              {
                                method: "DELETE",
                              }
                            );
                            if (!response.ok) {
                              const errorData = await response.json();
                              throw new Error(
                                errorData.error || "Delete failed"
                              );
                            }
                            await fetchData();
                            setSuccessMessage(
                              "Stock recheck deleted successfully."
                            );
                          } catch (error) {
                            console.error(
                              "Failed to delete stock recheck:",
                              error
                            );
                            setError(
                              "Failed to delete stock recheck. Please try again."
                            );
                          }
                        }
                      }}
                      className="text-red-600 hover:text-red-800 dark:hover:text-red-400 font-semibold"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Batch-wise Stock Table */}
      
    </div>
  );
};

export default StockRecheckDashboard;
