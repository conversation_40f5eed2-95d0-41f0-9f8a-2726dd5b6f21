import React, { useState, useEffect } from "react";
import axios from "axios";
import { toast } from "react-toastify";
import { useAuth } from "../../context/NewAuthContext";
import Select from "react-select";
// import PrintableInvoice from "./PrintableInvoice";
import PrintableInvoice from "../sales/PrintableInvoice";
// import PrintableQuotation from "./PrintableQuotation";
import PrintableQuotation from "../sales/PrintableQuotation";

// Custom styles for react-select
const customSelectStyles = {
  control: (provided, state) => ({
    ...provided,
    borderColor: state.isFocused ? "#2563eb" : "#d1d5db",
    boxShadow: state.isFocused ? "0 0 0 1px #2563eb" : "none",
    minHeight: "42px",
    borderRadius: "0.375rem",
    backgroundColor: state.isDisabled ? "#f3f4f6" : "white",
    "&:hover": {
      borderColor: "#2563eb",
    },
    fontSize: "0.875rem",
    color: "#111827",
    ...(state.selectProps.isDarkMode && {
      borderColor: state.isFocused ? "#60a5fa" : "#4b5563",
      backgroundColor: state.isDisabled ? "#374151" : "#1f2937",
      color: "#f9fafb",
    }),
  }),
  menu: (provided, state) => ({
    ...provided,
    zIndex: 9999,
    fontSize: "0.875rem",
    backgroundColor: "white",
    border: "1px solid #d1d5db",
    borderRadius: "0.375rem",
    ...(state.selectProps.isDarkMode && {
      backgroundColor: "#1f2937",
      border: "1px solid #4b5563",
    }),
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? "#2563eb"
      : state.isFocused
        ? "#e0e7ff"
        : "white",
    color: state.isSelected ? "white" : "#111827",
    cursor: "pointer",
    "&:hover": {
      backgroundColor: state.isSelected ? "#2563eb" : "#e0e7ff",
    },
    ...(state.selectProps.isDarkMode && {
      backgroundColor: state.isSelected
        ? "#3b82f6"
        : state.isFocused
          ? "#4b5563"
          : "#1f2937",
      color: state.isSelected ? "white" : "#f9fafb",
    }),
  }),
  placeholder: (provided, state) => ({
    ...provided,
    color: "#6b7280",
    ...(state.selectProps.isDarkMode && {
      color: "#9ca3af",
    }),
  }),
  singleValue: (provided, state) => ({
    ...provided,
    color: "#111827",
    ...(state.selectProps.isDarkMode && {
      color: "#f9fafb",
    }),
  }),
};

// ActionModal component
const ActionModal = ({
  show,
  onClose,
  onConfirm,
  item,
  selectedUser,
  setSelectedUser,
  users,
  isLoadingUsers,
  errorUsers,
  actionType,
}) => {
  if (!show) return null;
  const isReject = actionType === "Reject";
  const colorClass = isReject
    ? "bg-red-600 hover:bg-red-700 disabled:bg-red-300"
    : "bg-green-600 hover:bg-green-700 disabled:bg-green-300";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex justify-center items-center z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg w-full max-w-md shadow-xl relative">
        <button
          className="absolute top-4 right-4 text-gray-600 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400"
          onClick={onClose}
        >
          ✕
        </button>
        <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-gray-100">
          {actionType} {item?.type?.toUpperCase()} {item?.number}
        </h2>
        <p className="mb-4 text-gray-700 dark:text-gray-300">
          Please select the {isReject ? "rejector" : "approver"} to confirm the{" "}
          {isReject ? "rejection" : "approval"} of this {item?.type}.
        </p>
        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            {isReject ? "Rejector" : "Approver"}{" "}
            <span className="text-red-500">*</span>
          </label>
          <Select
            options={users.map((u) => ({ value: u.name, label: u.name }))}
            value={selectedUser}
            onChange={setSelectedUser}
            placeholder={
              isLoadingUsers
                ? "Loading users..."
                : errorUsers
                  ? "Error loading users"
                  : `Select ${isReject ? "rejector" : "approver"}`
            }
            isClearable
            isSearchable
            isDisabled={isLoadingUsers || !!errorUsers}
            styles={customSelectStyles}
            isDarkMode={document.documentElement.classList.contains("dark")}
          />
          {errorUsers && (
            <p className="mt-1 text-xs text-red-600 dark:text-red-400">
              {errorUsers}
            </p>
          )}
        </div>
        <div className="flex justify-end gap-2">
          <button
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className={`px-4 py-2 ${colorClass} text-white rounded disabled:cursor-not-allowed`}
            onClick={onConfirm}
            disabled={!selectedUser?.value}
          >
            Confirm {actionType}
          </button>
        </div>
      </div>
    </div>
  );
};

const Approvels = () => {
  const { user } = useAuth();
  const [selectedTab, setSelectedTab] = useState("invoices");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All");
  const [alertVisible, setAlertVisible] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [quotations, setQuotations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [errorUsers, setErrorUsers] = useState(null);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalItem, setApprovalItem] = useState(null);
  const [selectedApprover, setSelectedApprover] = useState(null);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [rejectionItem, setRejectionItem] = useState(null);
  const [selectedRejector, setSelectedRejector] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch invoices, quotations, and users
  useEffect(() => {
    const fetchData = async () => {
      if (!user?.token) {
        setError("User not authenticated. Please log in.");
        toast.error("Please log in to view approvals.");
        return;
      }

      setIsLoading(true);
      setIsLoadingUsers(true);
      setError(null);
      setErrorUsers(null);
      try {
        const [invoicesResponse, quotationsResponse, usersResponse] =
          await Promise.all([
            axios.get("http://127.0.0.1:8000/api/invoices", {
              headers: { Authorization: `Bearer ${user.token}` },
            }),
            axios.get("http://127.0.0.1:8000/api/quotations", {
              headers: { Authorization: `Bearer ${user.token}` },
            }),
            axios.get("http://127.0.0.1:8000/api/quotation-users", {
              headers: { Authorization: `Bearer ${user.token}` },
            }),
          ]);

        // Process invoices
        const invoicesData =
          invoicesResponse.data.data || invoicesResponse.data;
        setInvoices(
          invoicesData.map((inv) => ({
            id: inv.id,
            number: inv.invoice_no || `INV-${String(inv.id).padStart(3, "0")}`,
            amount: parseFloat(inv.total_amount) || 0,
            status: inv.rejected_by
              ? "Rejected"
              : inv.approved_by
                ? "Approved"
                : "Pending",
            preparedBy: inv.prepared_by || "Unknown",
            approvedBy: inv.approved_by || "",
            rejectedBy: inv.rejected_by || "N/A",
            createdAt: inv.created_at || new Date().toISOString(),
            customer: {
              id: inv.customer?.id || null,
              name: inv.customer_name || "Unknown Customer",
              address: inv.customer_address || "N/A",
              phone: inv.customer_phone || "N/A",
              email: inv.customer_email || "N/A",
            },
            items: Array.isArray(inv.items)
              ? inv.items.map((item) => ({
                  id: item.id || null,
                  description: item.description || item.product_name || "N/A",
                  quantity: parseFloat(item.quantity || item.qty || 1),
                  unit_price: parseFloat(
                    item.unit_price || item.sales_price || 0
                  ),
                  discountAmount: parseFloat(
                    item.discount || item.discount_amount || 0
                  ),
                  discountPercentage: parseFloat(item.discount_percentage || 0),
                  specialDiscount: parseFloat(item.special_discount || 0),
                  total:
                    parseFloat(
                      item.total ||
                        (item.quantity || 1) *
                          (item.unit_price || item.sales_price || 0)
                    ) || 0,
                  totalBuyingCost: parseFloat(item.total_buying_cost || 0),
                  freeQty: parseFloat(item.free_qty || 0),
                  mrp: parseFloat(item.mrp || 0),
                }))
              : [],
            subtotal: parseFloat(inv.subtotal) || 0,
            tax_amount: parseFloat(inv.tax_amount) || 0,
            discount: parseFloat(inv.discount_amount) || 0,
            purchase_amount: parseFloat(inv.purchase_amount) || 0,
            balance: parseFloat(inv.balance) || 0,
            payment_method: inv.payment_method || "Cash",
            invoice_date: inv.invoice_date || inv.created_at,
            invoice_time:
              inv.invoice_time ||
              new Date(inv.created_at).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              }),
            agent: {
              name: inv.agent_name || "",
              phone: inv.agent_phone || "",
            },
          }))
        );

        // Process quotations
        const quotationsData =
          quotationsResponse.data.data || quotationsResponse.data;
        setQuotations(
          quotationsData.map((q) => ({
            id: q.id,
            number: q.quotation_no || `QT-${String(q.id).padStart(3, "0")}`,
            amount: parseFloat(q.total) || 0,
            status: q.rejected_by
              ? "Rejected"
              : q.approved_by
                ? "Approved"
                : "Pending",
            preparedBy: q.prepared_by || "Unknown",
            approvedBy: q.approved_by || "",
            rejectedBy: q.rejected_by || "N/A",
            createdAt: q.created_at || new Date().toISOString(),
            customer: {
              id: q.customer?.id || null,
              name: q.customer_name || "Unknown Customer",
              address: q.customer_address || "N/A",
              phone: q.customer_phone || "N/A",
              email: q.customer_email || "N/A",
            },
            agent: {
              name: q.agent_name || "",
              phone: q.agent_phone || "",
            },
            items: Array.isArray(q.items)
              ? q.items.map((item) => ({
                  productId: item.product_id || null,
                  description: item.description || "N/A",
                  qty: parseFloat(item.qty || 1),
                  mrp: parseFloat(item.mrp || 0),
                  sellingPrice: parseFloat(item.selling_price || 0),
                  freeQty: parseFloat(item.free_qty || 0),
                  discountAmount: parseFloat(item.discount_amount || 0),
                  specialDiscountAmount: parseFloat(
                    item.special_discount_amount || 0
                  ),
                  discountPercentage: parseFloat(item.discount_percentage || 0),
                  discountSchemeType: item.discount_scheme_type || "",
                  total: parseFloat(item.total || 0),
                }))
              : [],
            subtotal: parseFloat(q.subtotal) || 0,
            tax_amount: parseFloat(q.tax_amount || q.tax || 0),
            discount: parseFloat(q.discount) || 0,
            purchase_amount: parseFloat(q.purchase_amount) || 0,
            balance: parseFloat(q.balance) || 0,
            payment_method: q.payment_method || "Cash",
            quotation_date: q.quotation_date || q.created_at,
            quotation_time:
              q.quotation_time ||
              new Date(q.created_at).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              }),
          }))
        );

        // Process users
        const usersData = usersResponse.data.data || usersResponse.data;
        setUsers(usersData);
      } catch (err) {
        console.error("Error fetching data:", err);
        const errorMessage =
          err.response?.data?.error || err.message || "Failed to load data.";
        setError(errorMessage);
        setErrorUsers(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
        setIsLoadingUsers(false);
      }
    };

    fetchData();
  }, [user?.token]);

  const handleApproveClick = (type, item) => {
    setApprovalItem({ type, ...item });
    const currentUserOption = users.find((u) => u.name === user?.name)
      ? { value: user.name, label: user.name }
      : null;
    setSelectedApprover(currentUserOption);
    setShowApprovalModal(true);
  };

  const handleConfirmApprove = async () => {
    if (isSubmitting) return;
    if (!user?.token) {
      toast.error("User not authenticated. Please log in.");
      setShowApprovalModal(false);
      return;
    }

    if (!selectedApprover?.value) {
      toast.error("Please select an approver.");
      return;
    }

    setIsSubmitting(true);
    try {
      const { type, id } = approvalItem;
      const endpoint =
        type === "invoice"
          ? `http://127.0.0.1:8000/api/invoices/${id}/approve`
          : `http://127.0.0.1:8000/api/quotations/${id}/approve`;
      const response = await axios.post(
        endpoint,
        { approved_by: selectedApprover.value },
        { headers: { Authorization: `Bearer ${user.token}` } }
      );

      if (type === "invoice") {
        setInvoices(
          invoices.map((inv) =>
            inv.id === id
              ? {
                  ...inv,
                  status: "Approved",
                  approvedBy: selectedApprover.value,
                  rejectedBy: "",
                }
              : inv
          )
        );
      } else {
        setQuotations(
          quotations.map((q) =>
            q.id === id
              ? {
                  ...q,
                  status: "Approved",
                  approvedBy: selectedApprover.value,
                  rejectedBy: "",
                }
              : q
          )
        );
      }

      toast.success(
        `${type.toUpperCase()} ${approvalItem.number} has been approved!`
      );
      setShowApprovalModal(false);
      setApprovalItem(null);
      setSelectedApprover(null);
    } catch (err) {
      console.error("Error approving item:", err);
      const status = err.response?.status;
      let errorMessage = `Failed to approve ${approvalItem.type} ${approvalItem.number}.`;
      if (status === 401) {
        errorMessage = "Unauthorized. Please log in again.";
      } else if (status === 404) {
        errorMessage = `${approvalItem.type.toUpperCase()} not found.`;
      } else if (err.response?.data?.error || err.response?.data?.message) {
        errorMessage = err.response.data.error || err.response.data.message;
      }
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRejectClick = (type, item) => {
    setRejectionItem({ type, ...item });
    const currentUserOption = users.find((u) => u.name === user?.name)
      ? { value: user.name, label: user.name }
      : null;
    setSelectedRejector(currentUserOption);
    setShowRejectionModal(true);
  };

  const handleConfirmReject = async () => {
    if (isSubmitting) return;
    if (!user?.token) {
      toast.error("User not authenticated. Please log in.");
      setShowRejectionModal(false);
      return;
    }

    if (!selectedRejector?.value) {
      toast.error("Please select a rejector.");
      return;
    }

    setIsSubmitting(true);
    try {
      const { type, id } = rejectionItem;
      const endpoint =
        type === "invoice"
          ? `http://127.0.0.1:8000/api/invoices/${id}/reject`
          : `http://127.0.0.1:8000/api/quotations/${id}/reject`;
      const response = await axios.post(
        endpoint,
        { rejected_by: selectedRejector.value },
        { headers: { Authorization: `Bearer ${user.token}` } }
      );

      if (type === "invoice") {
        setInvoices(
          invoices.map((inv) =>
            inv.id === id
              ? {
                  ...inv,
                  status: "Rejected",
                  approvedBy: "",
                  rejectedBy: selectedRejector.value,
                }
              : inv
          )
        );
      } else {
        setQuotations(
          quotations.map((q) =>
            q.id === id
              ? {
                  ...q,
                  status: "Rejected",
                  approvedBy: "",
                  rejectedBy: selectedRejector.value,
                }
              : q
          )
        );
      }

      toast.success(
        `${type.toUpperCase()} ${rejectionItem.number} has been rejected!`
      );
      setShowRejectionModal(false);
      setRejectionItem(null);
      setSelectedRejector(null);
    } catch (err) {
      console.error("Error rejecting item:", err);
      const status = err.response?.status;
      let errorMessage = `Failed to reject ${rejectionItem.type} ${rejectionItem.number}.`;
      if (status === 401) {
        errorMessage = "Unauthorized. Please log in again.";
      } else if (status === 404) {
        errorMessage = `${rejectionItem.type.toUpperCase()} not found.`;
      } else if (err.response?.data?.error || err.response?.data?.message) {
        errorMessage = err.response.data.error || err.response.data.message;
      }
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const filterAndSearch = (data) => {
    return data.filter((item) => {
      const matchSearch =
        item.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.preparedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.approvedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.rejectedBy.toLowerCase().includes(searchTerm.toLowerCase());
      const matchStatus =
        statusFilter === "All" || item.status === statusFilter;
      return matchSearch && matchStatus;
    });
  };

  const renderStatusTag = (status) => {
    const base = "px-2 py-1 rounded text-xs font-bold";
    switch (status) {
      case "Pending":
        return (
          <span className={`${base} bg-yellow-200 text-yellow-800`}>
            Pending
          </span>
        );
      case "Approved":
        return (
          <span className={`${base} bg-green-200 text-green-800`}>
            Approved
          </span>
        );
      case "Rejected":
        return (
          <span className={`${base} bg-red-200 text-red-800`}>Rejected</span>
        );
      default:
        return <span className={base}>{status}</span>;
    }
  };

  const openModal = (item) => {
    setSelectedItem({
      ...item,
      type: selectedTab === "invoices" ? "invoice" : "quotation",
    });
    setShowModal(true);
  };

  // Map selectedItem to PrintableInvoice or PrintableQuotation data format
  const mapToPrintableData = (item) => {
    if (!item) return null;

    if (item.type === "invoice") {
      return {
        invoiceData: {
          customer: {
            name: item.customer?.name || "Unknown Customer",
            address: item.customer?.address || "N/A",
            phone: item.customer?.phone || "N/A",
            email: item.customer?.email || "N/A",
          },
          items: (Array.isArray(item.items) ? item.items : []).map((i) => ({
            id: i.id || null,
            description: i.description || i.product_name || "N/A",
            quantity: parseFloat(i.quantity || i.qty || 1),
            unit_price: parseFloat(i.unit_price || i.sales_price || 0),
            discountAmount: parseFloat(i.discount || i.discount_amount || 0),
            discountPercentage: parseFloat(i.discount_percentage || 0),
            specialDiscount: parseFloat(i.special_discount || 0),
            total:
              parseFloat(
                i.total ||
                  (i.quantity || 1) * (i.unit_price || i.sales_price || 0)
              ) || 0,
            totalBuyingCost: parseFloat(i.totalBuyingCost || 0),
            freeQty: parseFloat(i.freeQty || 0),
            mrp: parseFloat(i.mrp || 0),
          })),
          footerDetails: {
            approvedBy: item.approvedBy || "N/A",
            nextApprovalTo: item.next_approval_to || "",
            dateTime: new Date(item.createdAt).toLocaleString(),
          },
          subtotal: parseFloat(item.subtotal) || 0,
          tax: parseFloat(item.tax_amount) || 0,
          discount: parseFloat(item.discount) || 0,
          total: parseFloat(item.amount) || 0,
          amountPaid: parseFloat(item.purchase_amount) || 0,
          balance: parseFloat(item.balance) || 0,
          invoice: {
            no: item.number,
            date: item.invoice_date
              ? new Date(item.invoice_date).toLocaleDateString()
              : new Date(item.createdAt).toLocaleDateString(),
            time:
              item.invoice_time ||
              new Date(item.createdAt).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              }),
          },
          paymentMethod: item.payment_method || "Cash",
          status: item.status || "Pending",
        },
      };
    } else {
      // Quotation
      return {
        quotationData: {
          quotation: {
            no: item.number,
            date: item.quotation_date
              ? new Date(item.quotation_date).toLocaleDateString()
              : new Date(item.createdAt).toLocaleDateString(),
            time:
              item.quotation_time ||
              new Date(item.createdAt).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              }),
          },
          customer: {
            id: item.customer?.id || null,
            name: item.customer?.name || "Unknown Customer",
            address: item.customer?.address || "N/A",
            phone: item.customer?.phone || "N/A",
            email: item.customer?.email || "N/A",
          },
          agent: {
            name: item.agent?.name || "",
            phone: item.agent?.phone || "",
          },
          items: (Array.isArray(item.items) ? item.items : []).map((i) => ({
            productId: i.productId || null,
            description: i.description || "N/A",
            qty: parseFloat(i.qty || 1),
            mrp: parseFloat(i.mrp || 0),
            sellingPrice: parseFloat(i.sellingPrice || 0),
            freeQty: parseFloat(i.freeQty || 0),
            discountAmount: parseFloat(i.discountAmount || 0),
            specialDiscountAmount: parseFloat(i.specialDiscountAmount || 0),
            discountPercentage: parseFloat(i.discountPercentage || 0),
            discountSchemeType: i.discountSchemeType || "",
            total: parseFloat(i.total || 0),
          })),
          footerDetails: {
            preparedBy: item.preparedBy || "",
            approvedBy: item.approvedBy || "",
            dateTime: new Date(item.createdAt).toLocaleString(),
          },
          total: {
            grandTotal: parseFloat(item.amount) || 0,
            totalDiscount: item.items.reduce(
              (sum, i) => sum + (parseFloat(i.discountAmount) || 0),
              0
            ),
            amountPaid: parseFloat(item.purchase_amount) || 0,
            balanceDue: parseFloat(item.balance) || 0,
            tax: parseFloat(item.tax_amount) || 0,
          },
        },
      };
    }
  };

  // Handle print functionality
  const handlePrint = () => {
    const printContent = document.getElementById("printable-area").innerHTML;
    const printWindow = window.open("", "_blank");
    printWindow.document.write(`
      <html>
        <head>
          <title>Print ${selectedItem?.type === "invoice" ? "Invoice" : "Quotation"}</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <script src="https://cdn.tailwindcss.com"></script>
          <style>
            @page {
              size: A4;
              margin: 10mm;
            }
            body {
              margin: 0;
              font-family: Arial, sans-serif;
              background-color: #ffffff;
            }
            .printable-area {
              width: 190mm;
              min-height: 277mm;
              margin: 0 auto;
              padding: 20px;
              box-sizing: border-box;
              background-color: #ffffff;
            }
            @media print {
              body {
                margin: 0;
              }
              .printable-area {
                width: 100%;
                margin: 0;
                padding: 10mm;
              }
              .no-print {
                display: none !important;
              }
              .bg-blue-600 { background-color: #2563eb !important; }
              .text-blue-600 { color: #2563eb !important; }
              .border-blue-600 { border-color: #2563eb !important; }
              .bg-blue-50 { background-color: #eff6ff !important; }
              .text-gray-900 { color: #1f2937 !important; }
              .text-gray-600 { color: #4b5563 !important; }
              .border-gray-200 { border-color: #e5e7eb !important; }
            }
          </style>
        </head>
        <body>
          <div class="printable-area">${printContent}</div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
  };

  const renderItems = (type, data) => {
    const filteredData = filterAndSearch(data);

    return (
      <div className="overflow-x-auto mt-4">
        <table className="min-w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-md">
          <thead>
            <tr className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 uppercase text-xs font-semibold">
              <th className="py-3 px-4 text-left">
                {type === "invoice" ? "Invoice No" : "Quotation No"}
              </th>
              <th className="py-3 px-4 text-left">Amount</th>
              <th className="py-3 px-4 text-left">Prepared By</th>
              <th className="py-3 px-4 text-left">Approved By</th>
              <th className="py-3 px-4 text-left">Rejected By</th>
              <th className="py-3 px-4 text-left">Status</th>
              <th className="py-3 px-4 text-left">Created At</th>
              <th className="py-3 px-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredData.map((item) => (
              <tr
                key={item.id}
                className="border-t border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                <td className="py-3 px-4 text-gray-800 dark:text-gray-100 font-semibold">
                  {item.number}
                </td>
                <td className="py-3 px-4 text-gray-600 dark:text-gray-300">
                  LKR {item.amount.toLocaleString()}
                </td>
                <td className="py-3 px-4 text-gray-600 dark:text-gray-300">
                  {item.preparedBy}
                </td>
                <td className="py-3 px-4 text-gray-600 dark:text-gray-300">
                  {item.approvedBy || "N/A"}
                </td>
                <td className="py-3 px-4 text-gray-600 dark:text-gray-300">
                  {item.rejectedBy || "N/A"}
                </td>
                <td className="py-3 px-4">{renderStatusTag(item.status)}</td>
                <td className="py-3 px-4 text-gray-600 dark:text-gray-300 text-xs">
                  {new Date(item.createdAt).toLocaleString()}
                </td>
                <td className="py-3 px-4 flex gap-2">
                  {item.status !== "Approved" && item.status !== "Rejected" && (
                    <>
                      <button
                        className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-xs"
                        onClick={() => handleApproveClick(type, item)}
                        disabled={isSubmitting}
                      >
                        Approve
                      </button>
                      <button
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-xs"
                        onClick={() => handleRejectClick(type, item)}
                        disabled={isSubmitting}
                      >
                        Reject
                      </button>
                    </>
                  )}
                  <button
                    className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 text-xs"
                    onClick={() => openModal(item)}
                  >
                    View
                  </button>
                </td>
              </tr>
            ))}
            {filteredData.length === 0 && (
              <tr>
                <td
                  colSpan="8"
                  className="py-4 text-center text-gray-500 dark:text-gray-400"
                >
                  No records found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
          Approvals
        </h1>
        {alertVisible &&
          (invoices.filter((inv) => inv.status === "Pending").length > 0 ||
            quotations.filter((q) => q.status === "Pending").length > 0) && (
            <div className="bg-yellow-100 text-yellow-800 px-4 py-2 rounded animate-pulse font-semibold shadow-sm">
              ⚠️ You have pending approvals!
            </div>
          )}
      </div>

      {/* Tabs */}
      <div className="flex gap-3 mb-6">
        {["invoices", "quotations"].map((tab) => (
          <button
            key={tab}
            onClick={() => setSelectedTab(tab)}
            className={`px-4 py-2 rounded-full text-sm font-medium capitalize ${
              selectedTab === tab
                ? "bg-blue-600 text-white dark:bg-blue-700"
                : "bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Filter/Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
        <input
          type="text"
          className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm w-full md:w-1/3 bg-white dark:bg-gray-800 dark:text-gray-100"
          placeholder="Search by number, prepared by, approved by, or rejected by..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <select
          className="px-4 py-2 border-gray-300 dark:border-gray-700 rounded-lg shadow-sm w-full md:w-auto bg-white dark:bg-gray-800 dark:text-gray-100"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="All">All Statuses</option>
          <option value="Pending">Pending</option>
          <option value="Approved">Approved</option>
          <option value="Rejected">Rejected</option>
        </select>
      </div>

      {/* Loading and Error States */}
      {isLoading && (
        <div className="text-center text-gray-600 dark:text-gray-400">
          Loading approvals...
        </div>
      )}
      {error && (
        <div className="text-center text-red-600 dark:text-red-400">
          {error}
        </div>
      )}
      {!isLoading && !error && (
        <>
          {selectedTab === "invoices" && renderItems("invoice", invoices)}
          {selectedTab === "quotations" && renderItems("quotation", quotations)}
        </>
      )}

      {/* View Modal */}
      {showModal && selectedItem && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4 bg-black bg-opacity-70 backdrop-blur-sm animate-fade-in">
          <div className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden bg-white dark:bg-gray-800 rounded-lg shadow-xl flex flex-col">
            <div className="flex items-center justify-between flex-shrink-0 p-6 border-b dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedItem.type === "invoice" ? "Invoice" : "Quotation"}{" "}
                Details (#{selectedItem.number})
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="p-2 text-gray-500 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 dark:text-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                aria-label="Close preview"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-6 h-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="flex-grow overflow-y-auto p-6" id="printable-area">
              {/* Approval Details Section */}
              <div className="mb-8">
                <h4 className="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">
                  Approval Details
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                  <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        {selectedItem.type} Number
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        {selectedItem.number}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        Status
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        {renderStatusTag(selectedItem.status)}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        Prepared By
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        {selectedItem.preparedBy || "N/A"}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        Approved By
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        {selectedItem.approvedBy || "N/A"}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        Rejected By
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        {selectedItem.rejectedBy || "N/A"}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        Created At
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        {new Date(selectedItem.createdAt).toLocaleString()}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-gray-600 dark:text-gray-300">
                        Total Amount
                      </dt>
                      <dd className="text-gray-900 dark:text-gray-100">
                        LKR {selectedItem.amount.toLocaleString()}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>

              {/* Printable Component Section */}
              <div className="mt-8">
                <h4 className="text-md font-semibold text-gray-800 dark:text-gray-100 mb-4">
                  {selectedItem.type === "invoice" ? "Invoice" : "Quotation"}{" "}
                  Preview
                </h4>
                {selectedItem.type === "invoice" ? (
                  <PrintableInvoice
                    invoiceData={mapToPrintableData(selectedItem)?.invoiceData}
                  />
                ) : (
                  <PrintableQuotation
                    quotation={
                      mapToPrintableData(selectedItem)?.quotationData.quotation
                    }
                    customer={
                      mapToPrintableData(selectedItem)?.quotationData.customer
                    }
                    agent={
                      mapToPrintableData(selectedItem)?.quotationData.agent
                    }
                    items={
                      mapToPrintableData(selectedItem)?.quotationData.items
                    }
                    footerDetails={
                      mapToPrintableData(selectedItem)?.quotationData
                        .footerDetails
                    }
                    total={
                      mapToPrintableData(selectedItem)?.quotationData.total
                    }
                  />
                )}
              </div>
            </div>
            <div className="flex flex-shrink-0 p-4 border-t justify-end dark:border-gray-700 gap-2">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 text-sm text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500"
              >
                Close
              </button>
              {/* <button
                onClick={handlePrint}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h2m2 0V4a2 2 0 00-2-2H7a2 2 0 00-2 2v4"
                  />
                </svg>
                Print
              </button> */}
            </div>
          </div>
        </div>
      )}

      {/* Approval Modal */}
      <ActionModal
        show={showApprovalModal}
        onClose={() => {
          setShowApprovalModal(false);
          setApprovalItem(null);
          setSelectedApprover(null);
        }}
        onConfirm={handleConfirmApprove}
        item={approvalItem}
        selectedUser={selectedApprover}
        setSelectedUser={setSelectedApprover}
        users={users}
        isLoadingUsers={isLoadingUsers}
        errorUsers={errorUsers}
        actionType="Approve"
      />

      {/* Rejection Modal */}
      <ActionModal
        show={showRejectionModal}
        onClose={() => {
          setShowRejectionModal(false);
          setRejectionItem(null);
          setSelectedRejector(null);
        }}
        onConfirm={handleConfirmReject}
        item={rejectionItem}
        selectedUser={selectedRejector}
        setSelectedUser={setSelectedRejector}
        users={users}
        isLoadingUsers={isLoadingUsers}
        errorUsers={errorUsers}
        actionType="Reject"
      />
    </div>
  );
};

export default Approvels;
